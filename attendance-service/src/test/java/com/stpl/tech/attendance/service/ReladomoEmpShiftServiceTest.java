package com.stpl.tech.attendance.service;

import com.stpl.tech.attendance.dto.RosteringDto.EmpShiftUpdateRequestDTO;
import com.stpl.tech.attendance.dto.RosteringDto.EmpShiftUpdateResponseDTO;
import com.stpl.tech.attendance.service.RosteringService.ReladomoEmpShiftService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDateTime;
import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Integration test for ReladomoEmpShiftService
 * This test verifies that the Reladomo bitemporal implementation is working correctly
 */
@SpringBootTest
@ActiveProfiles("test")
public class ReladomoEmpShiftServiceTest {

    @Autowired
    private ReladomoEmpShiftService reladomoEmpShiftService;

    @Test
    public void testUpdateEmpShifts_ValidRequest_ShouldReturnSuccess() {
        // Given
        EmpShiftUpdateRequestDTO request = EmpShiftUpdateRequestDTO.builder()
                .empIds(Arrays.asList(1, 2))
                .shiftIds(Arrays.asList(101, 102))
                .businessFrom(LocalDateTime.now())
                .businessTo(LocalDateTime.now().plusDays(30))
                .expectedArrivalTime(LocalDateTime.now().plusHours(1))
                .updatedBy("test-user")
                .build();

        // When
        EmpShiftUpdateResponseDTO response = reladomoEmpShiftService.updateEmpShifts(request);

        // Then
        assertNotNull(response);
        assertTrue(response.getSuccess());
        assertEquals("Shifts updated successfully using bitemporal operations", response.getMessage());
        assertEquals(request.getShiftIds(), response.getUpdatedShifts());
        assertEquals(request.getEmpIds(), response.getUpdatedEmployees());
        assertEquals(4, response.getTotalUpdatedMappings()); // 2 employees * 2 shifts = 4 mappings
    }

    @Test
    public void testUpdateEmpShifts_InvalidRequest_ShouldThrowException() {
        // Given
        EmpShiftUpdateRequestDTO request = EmpShiftUpdateRequestDTO.builder()
                .empIds(null) // Invalid: null empIds
                .shiftIds(Arrays.asList(101))
                .businessFrom(LocalDateTime.now())
                .businessTo(LocalDateTime.now().plusDays(30))
                .updatedBy("test-user")
                .build();

        // When & Then
        assertThrows(IllegalArgumentException.class, () -> {
            reladomoEmpShiftService.updateEmpShifts(request);
        });
    }

    @Test
    public void testUpdateEmpShifts_BusinessFromAfterBusinessTo_ShouldThrowException() {
        // Given
        EmpShiftUpdateRequestDTO request = EmpShiftUpdateRequestDTO.builder()
                .empIds(Arrays.asList(1))
                .shiftIds(Arrays.asList(101))
                .businessFrom(LocalDateTime.now().plusDays(30)) // After businessTo
                .businessTo(LocalDateTime.now()) // Before businessFrom
                .updatedBy("test-user")
                .build();

        // When & Then
        assertThrows(IllegalArgumentException.class, () -> {
            reladomoEmpShiftService.updateEmpShifts(request);
        });
    }
}
