-- V1.0.1__create_approval_tables.sql
CREATE TABLE APPROVAL_REQUEST (
    APPROVAL_REQUEST_ID VARCHAR(36) NOT NULL,
    REQUEST_TYPE VARCHAR(50) NOT NULL,
    REQUESTER_ID VARCHAR(36) NOT NULL,
    TOTAL_STEPS INT NOT NULL,
    CURRENT_STEP INT NOT NULL,
    STATUS VARCHAR(20) NOT NULL,
    PROCESS_INSTANCE_ID VARCHAR(36) NULL,
    APPROVAL_REQUEST_STATUS VARCHAR(10) DEFAULT 'ACTIVE',
    CREATED_DATE TIMESTAMP NOT NULL,
    CREATED_BY VARCHAR(36) NOT NULL,
    UPDATED_DATE TIMESTAMP NOT NULL,
    UPDATED_BY VARCHAR(36) NOT NULL,
    DELETED_DATE TIMESTAMP NULL,
    DELETED_BY VARCHAR(36) NULL,
    PRIMARY KEY (APPROVAL_REQUEST_ID)
);

CREATE TABLE APPROVAL_STEP (
    APPROVAL_STEP_ID VARCHAR(36) NOT NULL,
    APPROVAL_REQUEST_ID VARCHAR(36) NOT NULL,
    STEP_NUMBER INT NOT NULL,
    APPROVER_ID VARCHAR(36) NULL,
    STATUS VARCHAR(20) NOT NULL,
    ACTION VARCHAR(20) NULL,
    COMMENTS TEXT NULL,
    APPROVAL_STEP_STATUS VARCHAR(10) DEFAULT 'ACTIVE',
    CREATED_DATE TIMESTAMP NOT NULL,
    CREATED_BY VARCHAR(36) NOT NULL,
    UPDATED_DATE TIMESTAMP NOT NULL,
    UPDATED_BY VARCHAR(36) NOT NULL,
    DELETED_DATE TIMESTAMP NULL,
    DELETED_BY VARCHAR(36) NULL,
    PRIMARY KEY (APPROVAL_STEP_ID),
    CONSTRAINT FK_AS_APPROVAL_REQUEST_ID FOREIGN KEY (APPROVAL_REQUEST_ID) REFERENCES APPROVAL_REQUEST(APPROVAL_REQUEST_ID)
);

CREATE INDEX IDX_AR_REQUEST_TYPE ON APPROVAL_REQUEST(REQUEST_TYPE) USING BTREE;
CREATE INDEX IDX_AR_STATUS ON APPROVAL_REQUEST(STATUS) USING BTREE;
CREATE INDEX IDX_AR_PROCESS_INSTANCE_ID ON APPROVAL_REQUEST(PROCESS_INSTANCE_ID) USING BTREE;
CREATE INDEX IDX_AS_APPROVAL_REQUEST_ID ON APPROVAL_STEP(APPROVAL_REQUEST_ID) USING BTREE;
CREATE INDEX IDX_AS_APPROVER_ID ON APPROVAL_STEP(APPROVER_ID) USING BTREE;
CREATE INDEX IDX_AS_STATUS ON APPROVAL_STEP(STATUS) USING BTREE;

-- V1.0.2__create_notification_tables.sql
CREATE TABLE NOTIFICATION (
    NOTIFICATION_ID VARCHAR(36) NOT NULL,
    TYPE VARCHAR(50) NOT NULL,
    TITLE VARCHAR(200) NOT NULL,
    CONTENT TEXT NOT NULL,
    REFERENCE_ID VARCHAR(36) NULL,
    PRIORITY VARCHAR(20) NOT NULL,
    NOTIFICATION_STATUS VARCHAR(10) DEFAULT 'ACTIVE',
    CREATED_DATE TIMESTAMP NOT NULL,
    CREATED_BY VARCHAR(36) NOT NULL,
    UPDATED_DATE TIMESTAMP NOT NULL,
    UPDATED_BY VARCHAR(36) NOT NULL,
    DELETED_DATE TIMESTAMP NULL,
    DELETED_BY VARCHAR(36) NULL,
    PRIMARY KEY (NOTIFICATION_ID)
);

CREATE TABLE NOTIFICATION_RECIPIENT (
    RECIPIENT_ID VARCHAR(36) NOT NULL,
    NOTIFICATION_ID VARCHAR(36) NOT NULL,
    USER_ID VARCHAR(36) NOT NULL,
    STATUS VARCHAR(20) NOT NULL,
    ERROR TEXT NULL,
    NOTIFICATION_RECIPIENT_STATUS VARCHAR(10) DEFAULT 'ACTIVE',
    CREATED_DATE TIMESTAMP NOT NULL,
    CREATED_BY VARCHAR(36) NOT NULL,
    UPDATED_DATE TIMESTAMP NOT NULL,
    UPDATED_BY VARCHAR(36) NOT NULL,
    DELETED_DATE TIMESTAMP NULL,
    DELETED_BY VARCHAR(36) NULL,
    PRIMARY KEY (RECIPIENT_ID),
    CONSTRAINT FK_NR_NOTIFICATION_ID FOREIGN KEY (NOTIFICATION_ID) REFERENCES NOTIFICATION(NOTIFICATION_ID)
);

CREATE INDEX IDX_N_TYPE ON NOTIFICATION(TYPE) USING BTREE;
CREATE INDEX IDX_N_PRIORITY ON NOTIFICATION(PRIORITY) USING BTREE;
CREATE INDEX IDX_NR_NOTIFICATION_ID ON NOTIFICATION_RECIPIENT(NOTIFICATION_ID) USING BTREE;
CREATE INDEX IDX_NR_USER_ID ON NOTIFICATION_RECIPIENT(USER_ID) USING BTREE;
CREATE INDEX IDX_NR_STATUS ON NOTIFICATION_RECIPIENT(STATUS) USING BTREE;

-- V1.0.3__create_flowable_tables.sql
-- Note: Flowable will automatically create its required tables when the application starts
-- This is just for documentation purposes

-- Flowable Process Engine Tables
-- ACT_RE_* : Repository tables
-- ACT_RU_* : Runtime tables
-- ACT_ID_* : Identity tables
-- ACT_HI_* : History tables
-- ACT_GE_* : General tables

-- V1.0.4__create_biometric_template_table.sql
CREATE TABLE BIOMETRIC_TEMPLATE (
    BIOMETRIC_TEMPLATE_ID INTEGER NOT NULL AUTO_INCREMENT,
    EMP_ID INTEGER NOT NULL,
    UNIT_ID INTEGER NOT NULL, -- Unit where template was registered
    EXTERNAL_TEMPLATE_ID VARCHAR(100) NULL, -- NULL until approved
    TEMPLATE_VERSION VARCHAR(50) NOT NULL,
    TEMPLATE_DATA BLOB NOT NULL,
    ENROLLMENT_DEVICE_ID VARCHAR(100) NULL,
    ENROLLMENT_DEVICE_TYPE VARCHAR(50) NULL, -- MOBILE, TABLET, KIOSK, etc.
    ENROLLMENT_DEVICE_MODEL VARCHAR(100) NULL,
    ENROLLMENT_DEVICE_OS VARCHAR(50) NULL,
    ENROLLMENT_DEVICE_OS_VERSION VARCHAR(50) NULL,
    ENROLLMENT_DATE TIMESTAMP NOT NULL,
    ENROLLMENT_IP_ADDRESS VARCHAR(50) NULL,
    ENROLLMENT_LOCATION_LAT DECIMAL(10,8) NULL,
    ENROLLMENT_LOCATION_LONG DECIMAL(11,8) NULL,
    APPROVAL_STATUS VARCHAR(20) DEFAULT 'PENDING', -- PENDING, APPROVED, REJECTED
    APPROVER_ID VARCHAR(36) NULL,
    APPROVAL_DATE TIMESTAMP NULL,
    REJECTION_REASON TEXT NULL,
    BIOMETRIC_TEMPLATE_STATUS VARCHAR(10) DEFAULT 'ACTIVE',
    CREATED_DATE TIMESTAMP NOT NULL,
    CREATED_BY VARCHAR(36) NOT NULL,
    UPDATED_DATE TIMESTAMP NOT NULL,
    UPDATED_BY VARCHAR(36) NOT NULL,
    DELETED_DATE TIMESTAMP NULL,
    DELETED_BY VARCHAR(36) NULL,
    PRIMARY KEY (BIOMETRIC_TEMPLATE_ID)
);

CREATE INDEX IDX_BT_EMP_ID ON BIOMETRIC_TEMPLATE(EMP_ID) USING BTREE;
CREATE INDEX IDX_BT_UNIT_ID ON BIOMETRIC_TEMPLATE(UNIT_ID) USING BTREE;
CREATE INDEX IDX_BT_EXTERNAL_TEMPLATE_ID ON BIOMETRIC_TEMPLATE(EXTERNAL_TEMPLATE_ID) USING BTREE;
CREATE INDEX IDX_BT_APPROVAL_STATUS ON BIOMETRIC_TEMPLATE(APPROVAL_STATUS) USING BTREE;

-- V1.0.5__create_biometric_template_metrics_table.sql
CREATE TABLE BIOMETRIC_TEMPLATE_METRICS (
    METRIC_ID INTEGER NOT NULL AUTO_INCREMENT,
    BIOMETRIC_TEMPLATE_ID INTEGER NOT NULL,
    TEMPLATE_QUALITY_SCORE DECIMAL(5,2) NULL,
    TEMPLATE_FEATURES_COUNT INT NULL,
    ENROLLMENT_LIGHTING_CONDITION VARCHAR(50) NULL, -- GOOD, AVERAGE, POOR
    LAST_UPDATE_DATE TIMESTAMP NULL,
    LAST_SUCCESSFUL_MATCH_DATE TIMESTAMP NULL,
    SUCCESSFUL_MATCH_COUNT INT DEFAULT 0,
    FAILED_MATCH_COUNT INT DEFAULT 0,
    AVERAGE_MATCH_SCORE DECIMAL(5,2) NULL,
    BIOMETRIC_TEMPLATE_METRICS_STATUS VARCHAR(10) DEFAULT 'ACTIVE',
    CREATED_DATE TIMESTAMP NOT NULL,
    CREATED_BY VARCHAR(36) NOT NULL,
    UPDATED_DATE TIMESTAMP NOT NULL,
    UPDATED_BY VARCHAR(36) NOT NULL,
    DELETED_DATE TIMESTAMP NULL,
    DELETED_BY VARCHAR(36) NULL,
    PRIMARY KEY (METRIC_ID),
    CONSTRAINT FK_BTM_BIOMETRIC_TEMPLATE_ID FOREIGN KEY (BIOMETRIC_TEMPLATE_ID) REFERENCES BIOMETRIC_TEMPLATE(BIOMETRIC_TEMPLATE_ID)
);

CREATE INDEX IDX_BTM_BIOMETRIC_TEMPLATE_ID ON BIOMETRIC_TEMPLATE_METRICS(BIOMETRIC_TEMPLATE_ID) USING BTREE;
CREATE INDEX IDX_BTM_TEMPLATE_QUALITY ON BIOMETRIC_TEMPLATE_METRICS(TEMPLATE_QUALITY_SCORE) USING BTREE;
CREATE INDEX IDX_BTM_MATCH_METRICS ON BIOMETRIC_TEMPLATE_METRICS(SUCCESSFUL_MATCH_COUNT, FAILED_MATCH_COUNT) USING BTREE;

