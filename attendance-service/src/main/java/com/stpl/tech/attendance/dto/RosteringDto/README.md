# Rostering DTOs Usage Guide

## Shift Management

### Creating a Shift

**Request DTO (ShiftRequestDTO)** - Only these fields are required in the payload:
```json
{
  "shiftName": "Morning Shift",
  "startTime": "2024-01-15T09:00:00",
  "endTime": "2024-01-15T17:00:00"
}
```

**Response DTO (ShiftResponseDTO)** - Complete shift information returned:
```json
{
  "shiftId": 1,
  "shiftName": "Morning Shift",
  "startTime": "2024-01-15T09:00:00",
  "endTime": "2024-01-15T17:00:00",
  "status": "ACTIVE",
  "createdBy": "12345",
  "creationTime": "2024-01-15T08:30:00",
  "updatedBy": "12345",
  "updationTime": "2024-01-15T08:30:00"
}
```

### Key Features:

1. **Auto-generated shiftId**: The shiftId is automatically generated by the database and returned in the response.

2. **Employee ID from HttpServletRequest**: The `createdBy` and `updatedBy` fields are automatically populated from the HttpServletRequest using the `RequestContextUtil.extractEmployeeId()` method.

3. **System timestamps**: The `creationTime` and `updationTime` are automatically set by the database using current system time.

4. **Default status**: The status is automatically set to "ACTIVE" for new shifts.

### API Endpoints:

- **POST /api/v1/roster/shifts** - Create a new shift
- **PUT /api/v1/roster/shifts/{shiftId}** - Update an existing shift
- **GET /api/v1/roster/shifts** - Get all shifts
- **DELETE /api/v1/roster/shifts/{shiftId}** - Delete a shift (soft delete)

### Employee ID Extraction:

The `RequestContextUtil.extractEmployeeId()` method supports multiple ways to extract employee ID:

1. **JWT Token**: From Authorization header
2. **Request Attribute**: Set by authentication filter
3. **Custom Header**: X-Employee-Id header
4. **Session**: From HTTP session
5. **Request Parameter**: For testing purposes (empId parameter)

You can customize the extraction logic in `RequestContextUtil.java` based on your authentication mechanism.

### Example Usage:

```bash
# Create a shift
curl -X POST http://localhost:8080/api/v1/roster/shifts \
  -H "Content-Type: application/json" \
  -H "X-Employee-Id: 12345" \
  -d '{
    "shiftName": "Evening Shift",
    "startTime": "2024-01-15T14:00:00",
    "endTime": "2024-01-15T22:00:00"
  }'

# Update a shift
curl -X PUT http://localhost:8080/api/v1/roster/shifts/1 \
  -H "Content-Type: application/json" \
  -H "X-Employee-Id: 12345" \
  -d '{
    "shiftName": "Updated Morning Shift",
    "startTime": "2024-01-15T08:00:00",
    "endTime": "2024-01-15T16:00:00"
  }'
```
