package com.stpl.tech.attendance.dto.RosteringDto;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmpShiftUpdateRequestDTO {

    @NotEmpty(message = "Employee IDs are required")
    private List<Integer> empIds;           // ← Multiple employees

    @NotEmpty(message = "Shift IDs are required")
    private List<Integer> shiftIds;         // ← Multiple shifts

    @NotNull(message = "Business from date is required")
    private LocalDateTime businessFrom;     // ← When the fact becomes true

    @NotNull(message = "Business to date is required")
    private LocalDateTime businessTo;       // ← When the fact ends

    private Boolean updateUpcomingShifts = true;  // ← Flag for future updates

    private LocalDateTime expectedArrivalTime;    // ← Expected arrival time

    private String updatedBy;  // ← Will be set from JWT context in controller
}
