package com.stpl.tech.attendance.repository.RosteringRepository;

import com.stpl.tech.attendance.entity.RosteringEntity.ShiftCoveragePlan;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface ShiftCoveragePlanRepository extends JpaRepository<ShiftCoveragePlan, Integer> {

    /**
     * Find shift coverage plans by shift cafe mapping ID
     */
    List<ShiftCoveragePlan> findByShiftCafeMappingId(Integer shiftCafeMappingId);

    /**
     * Find shift coverage plans by day
     */
    List<ShiftCoveragePlan> findByDay(Integer day);

    /**
     * Find active shift coverage plans by shift cafe mapping ID
     */
    List<ShiftCoveragePlan> findByShiftCafeMappingIdAndStatus(Integer shiftCafeMappingId, String status);

    /**
     * Find shift coverage plans by shift cafe mapping ID and day
     */
    ShiftCoveragePlan findByShiftCafeMappingIdAndDay(Integer shiftCafeMappingId, Integer day);

    /**
     * Find shift coverage plans within business date range
     */
    @Query("SELECT scp FROM ShiftCoveragePlan scp WHERE scp.shiftCafeMappingId = :shiftCafeMappingId " +
           "AND scp.businessFrom <= :businessDate AND (scp.businessTo IS NULL OR scp.businessTo > :businessDate)")
    List<ShiftCoveragePlan> findByShiftCafeMappingIdAndBusinessDate(@Param("shiftCafeMappingId") Integer shiftCafeMappingId, 
                                                                   @Param("businessDate") LocalDateTime businessDate);

    /**
     * Find shift coverage plans within processing date range
     */
    @Query("SELECT scp FROM ShiftCoveragePlan scp WHERE scp.shiftCafeMappingId = :shiftCafeMappingId " +
           "AND scp.processingFrom <= :processingDate AND (scp.processingTo IS NULL OR scp.processingTo > :processingDate)")
    List<ShiftCoveragePlan> findByShiftCafeMappingIdAndProcessingDate(@Param("shiftCafeMappingId") Integer shiftCafeMappingId, 
                                                                     @Param("processingDate") LocalDateTime processingDate);
} 