package com.stpl.tech.attendance.controller.RosteringController;

import com.stpl.tech.attendance.controller.BaseController;
import com.stpl.tech.attendance.entity.RosteringEntity.ShiftCoveragePlan;
import com.stpl.tech.attendance.model.response.ApiResponse;
import com.stpl.tech.attendance.service.RosteringService.ShiftCoveragePlanService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

@RestController
@RequestMapping("/api/v1/shift-coverage-plan")
@RequiredArgsConstructor
@Slf4j
public class ShiftCoveragePlanController extends BaseController {

    private final ShiftCoveragePlanService shiftCoveragePlanService;

    /**
     * Create a new shift coverage plan
     */
    @PostMapping
    public ResponseEntity<ApiResponse<ShiftCoveragePlan>> createShiftCoveragePlan(@RequestBody ShiftCoveragePlan shiftCoveragePlan) {
        log.info("Creating shift coverage plan for shift cafe mapping ID: {}", shiftCoveragePlan.getShiftCafeMappingId());
        
        ShiftCoveragePlan createdPlan = shiftCoveragePlanService.createShiftCoveragePlan(shiftCoveragePlan);
        
        return created(createdPlan);
    }

    /**
     * Update an existing shift coverage plan
     */
    @PutMapping("/{shiftCafeMappingId}")
    public ResponseEntity<ApiResponse<ShiftCoveragePlan>> updateShiftCoveragePlan(
            @PathVariable Integer shiftCafeMappingId,
            @RequestBody ShiftCoveragePlan shiftCoveragePlan) {
        log.info("Updating shift coverage plan with ID: {}", shiftCafeMappingId);
        
        shiftCoveragePlan.setShiftCafeMappingId(shiftCafeMappingId);
        ShiftCoveragePlan updatedPlan = shiftCoveragePlanService.updateShiftCoveragePlan(shiftCoveragePlan);
        
        return success(updatedPlan);
    }

    /**
     * Get shift coverage plan by ID
     */
    @GetMapping("/{shiftCafeMappingId}")
    public ResponseEntity<ApiResponse<ShiftCoveragePlan>> getShiftCoveragePlanById(@PathVariable Integer shiftCafeMappingId) {
        log.info("Fetching shift coverage plan by ID: {}", shiftCafeMappingId);
        
        ShiftCoveragePlan shiftCoveragePlan = shiftCoveragePlanService.getShiftCoveragePlanById(shiftCafeMappingId);
        
        return success(shiftCoveragePlan);
    }

    /**
     * Get all shift coverage plans by shift cafe mapping ID
     */
    @GetMapping("/shift-cafe-mapping/{shiftCafeMappingId}")
    public ResponseEntity<ApiResponse<List<ShiftCoveragePlan>>> getShiftCoveragePlansByShiftCafeMappingId(
            @PathVariable Integer shiftCafeMappingId) {
        log.info("Fetching shift coverage plans by shift cafe mapping ID: {}", shiftCafeMappingId);
        
        List<ShiftCoveragePlan> plans = shiftCoveragePlanService.getShiftCoveragePlansByShiftCafeMappingId(shiftCafeMappingId);
        
        return success(plans);
    }

    /**
     * Get shift coverage plans by day
     */
    @GetMapping("/day/{day}")
    public ResponseEntity<ApiResponse<List<ShiftCoveragePlan>>> getShiftCoveragePlansByDay(@PathVariable Integer day) {
        log.info("Fetching shift coverage plans by day: {}", day);
        
        List<ShiftCoveragePlan> plans = shiftCoveragePlanService.getShiftCoveragePlansByDay(day);
        
        return success(plans);
    }

    /**
     * Get active shift coverage plans by shift cafe mapping ID
     */
    @GetMapping("/shift-cafe-mapping/{shiftCafeMappingId}/active")
    public ResponseEntity<ApiResponse<List<ShiftCoveragePlan>>> getActiveShiftCoveragePlansByShiftCafeMappingId(
            @PathVariable Integer shiftCafeMappingId) {
        log.info("Fetching active shift coverage plans by shift cafe mapping ID: {}", shiftCafeMappingId);
        
        List<ShiftCoveragePlan> plans = shiftCoveragePlanService.getActiveShiftCoveragePlansByShiftCafeMappingId(shiftCafeMappingId);
        
        return success(plans);
    }

    /**
     * Get shift coverage plan by shift cafe mapping ID and day
     */
    @GetMapping("/shift-cafe-mapping/{shiftCafeMappingId}/day/{day}")
    public ResponseEntity<ApiResponse<ShiftCoveragePlan>> getShiftCoveragePlanByShiftCafeMappingIdAndDay(
            @PathVariable Integer shiftCafeMappingId,
            @PathVariable Integer day) {
        log.info("Fetching shift coverage plan by shift cafe mapping ID: {} and day: {}", shiftCafeMappingId, day);
        
        ShiftCoveragePlan plan = shiftCoveragePlanService.getShiftCoveragePlanByShiftCafeMappingIdAndDay(shiftCafeMappingId, day);
        
        return success(plan);
    }

    /**
     * Get shift coverage plans within business date range
     */
    @GetMapping("/shift-cafe-mapping/{shiftCafeMappingId}/business-date")
    public ResponseEntity<ApiResponse<List<ShiftCoveragePlan>>> getShiftCoveragePlansByShiftCafeMappingIdAndBusinessDate(
            @PathVariable Integer shiftCafeMappingId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime businessDate) {
        log.info("Fetching shift coverage plans by shift cafe mapping ID: {} and business date: {}", shiftCafeMappingId, businessDate);
        
        List<ShiftCoveragePlan> plans = shiftCoveragePlanService.getShiftCoveragePlansByShiftCafeMappingIdAndBusinessDate(shiftCafeMappingId, businessDate);
        
        return success(plans);
    }

    /**
     * Get shift coverage plans within processing date range
     */
    @GetMapping("/shift-cafe-mapping/{shiftCafeMappingId}/processing-date")
    public ResponseEntity<ApiResponse<List<ShiftCoveragePlan>>> getShiftCoveragePlansByShiftCafeMappingIdAndProcessingDate(
            @PathVariable Integer shiftCafeMappingId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime processingDate) {
        log.info("Fetching shift coverage plans by shift cafe mapping ID: {} and processing date: {}", shiftCafeMappingId, processingDate);
        
        List<ShiftCoveragePlan> plans = shiftCoveragePlanService.getShiftCoveragePlansByShiftCafeMappingIdAndProcessingDate(shiftCafeMappingId, processingDate);
        
        return success(plans);
    }

    /**
     * Delete shift coverage plan by ID
     */
    @DeleteMapping("/{shiftCafeMappingId}")
    public ResponseEntity<ApiResponse<Void>> deleteShiftCoveragePlan(@PathVariable Integer shiftCafeMappingId) {
        log.info("Deleting shift coverage plan with ID: {}", shiftCafeMappingId);
        
        shiftCoveragePlanService.deleteShiftCoveragePlan(shiftCafeMappingId);
        
        return noContent();
    }

    /**
     * Deactivate shift coverage plan (soft delete)
     */
    @PatchMapping("/{shiftCafeMappingId}/deactivate")
    public ResponseEntity<ApiResponse<Void>> deactivateShiftCoveragePlan(@PathVariable Integer shiftCafeMappingId) {
        log.info("Deactivating shift coverage plan with ID: {}", shiftCafeMappingId);
        
        shiftCoveragePlanService.deactivateShiftCoveragePlan(shiftCafeMappingId);
        
        return noContent();
    }
} 