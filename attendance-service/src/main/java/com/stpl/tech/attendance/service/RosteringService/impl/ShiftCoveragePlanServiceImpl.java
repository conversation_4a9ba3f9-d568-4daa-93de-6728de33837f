package com.stpl.tech.attendance.service.RosteringService.impl;

import com.stpl.tech.attendance.entity.RosteringEntity.ShiftCoveragePlan;
import com.stpl.tech.attendance.exception.AttendanceException;
import com.stpl.tech.attendance.enums.AttendanceErrorCode;
import com.stpl.tech.attendance.repository.RosteringRepository.ShiftCoveragePlanRepository;
import com.stpl.tech.attendance.service.RosteringService.ShiftCoveragePlanService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class ShiftCoveragePlanServiceImpl implements ShiftCoveragePlanService {

    private final ShiftCoveragePlanRepository shiftCoveragePlanRepository;

    @Override
    public ShiftCoveragePlan createShiftCoveragePlan(ShiftCoveragePlan shiftCoveragePlan) {
        log.info("Creating shift coverage plan for shift cafe mapping ID: {}", shiftCoveragePlan.getShiftCafeMappingId());
        
        // Set default values
        shiftCoveragePlan.setStatus("ACTIVE");
        shiftCoveragePlan.setCreationTime(LocalDateTime.now());
        
        // Set bitemporal dates if not provided
        if (shiftCoveragePlan.getBusinessFrom() == null) {
            shiftCoveragePlan.setBusinessFrom(LocalDateTime.now());
        }
        if (shiftCoveragePlan.getProcessingFrom() == null) {
            shiftCoveragePlan.setProcessingFrom(LocalDateTime.now());
        }
        
        return shiftCoveragePlanRepository.save(shiftCoveragePlan);
    }

    @Override
    public ShiftCoveragePlan updateShiftCoveragePlan(ShiftCoveragePlan shiftCoveragePlan) {
        log.info("Updating shift coverage plan with ID: {}", shiftCoveragePlan.getShiftCafeMappingId());
        
        ShiftCoveragePlan existingPlan = getShiftCoveragePlanById(shiftCoveragePlan.getShiftCafeMappingId());
        
        // Update fields
        existingPlan.setDay(shiftCoveragePlan.getDay());
        existingPlan.setIdealCount(shiftCoveragePlan.getIdealCount());
        existingPlan.setStatus(shiftCoveragePlan.getStatus());
        existingPlan.setUpdatedBy(shiftCoveragePlan.getUpdatedBy());
        existingPlan.setUpdationTime(LocalDateTime.now());
        
        // Update bitemporal dates if provided
        if (shiftCoveragePlan.getBusinessFrom() != null) {
            existingPlan.setBusinessFrom(shiftCoveragePlan.getBusinessFrom());
        }
        if (shiftCoveragePlan.getBusinessTo() != null) {
            existingPlan.setBusinessTo(shiftCoveragePlan.getBusinessTo());
        }
        if (shiftCoveragePlan.getProcessingFrom() != null) {
            existingPlan.setProcessingFrom(shiftCoveragePlan.getProcessingFrom());
        }
        if (shiftCoveragePlan.getProcessingTo() != null) {
            existingPlan.setProcessingTo(shiftCoveragePlan.getProcessingTo());
        }
        
        return shiftCoveragePlanRepository.save(existingPlan);
    }

    @Override
    @Transactional(readOnly = true)
    public ShiftCoveragePlan getShiftCoveragePlanById(Integer shiftCafeMappingId) {
        log.debug("Fetching shift coverage plan by ID: {}", shiftCafeMappingId);
        
        return shiftCoveragePlanRepository.findById(shiftCafeMappingId)
                .orElseThrow(() -> new AttendanceException(AttendanceErrorCode.SHIFT_COVERAGE_PLAN_NOT_FOUND, 
                    "Shift coverage plan not found with ID: " + shiftCafeMappingId));
    }

    @Override
    @Transactional(readOnly = true)
    public List<ShiftCoveragePlan> getShiftCoveragePlansByShiftCafeMappingId(Integer shiftCafeMappingId) {
        log.debug("Fetching shift coverage plans by shift cafe mapping ID: {}", shiftCafeMappingId);
        
        return shiftCoveragePlanRepository.findByShiftCafeMappingId(shiftCafeMappingId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<ShiftCoveragePlan> getShiftCoveragePlansByDay(Integer day) {
        log.debug("Fetching shift coverage plans by day: {}", day);
        
        return shiftCoveragePlanRepository.findByDay(day);
    }

    @Override
    @Transactional(readOnly = true)
    public List<ShiftCoveragePlan> getActiveShiftCoveragePlansByShiftCafeMappingId(Integer shiftCafeMappingId) {
        log.debug("Fetching active shift coverage plans by shift cafe mapping ID: {}", shiftCafeMappingId);
        
        return shiftCoveragePlanRepository.findByShiftCafeMappingIdAndStatus(shiftCafeMappingId, "ACTIVE");
    }

    @Override
    @Transactional(readOnly = true)
    public ShiftCoveragePlan getShiftCoveragePlanByShiftCafeMappingIdAndDay(Integer shiftCafeMappingId, Integer day) {
        log.debug("Fetching shift coverage plan by shift cafe mapping ID: {} and day: {}", shiftCafeMappingId, day);
        
        return shiftCoveragePlanRepository.findByShiftCafeMappingIdAndDay(shiftCafeMappingId, day);
    }

    @Override
    @Transactional(readOnly = true)
    public List<ShiftCoveragePlan> getShiftCoveragePlansByShiftCafeMappingIdAndBusinessDate(Integer shiftCafeMappingId, LocalDateTime businessDate) {
        log.debug("Fetching shift coverage plans by shift cafe mapping ID: {} and business date: {}", shiftCafeMappingId, businessDate);
        
        return shiftCoveragePlanRepository.findByShiftCafeMappingIdAndBusinessDate(shiftCafeMappingId, businessDate);
    }

    @Override
    @Transactional(readOnly = true)
    public List<ShiftCoveragePlan> getShiftCoveragePlansByShiftCafeMappingIdAndProcessingDate(Integer shiftCafeMappingId, LocalDateTime processingDate) {
        log.debug("Fetching shift coverage plans by shift cafe mapping ID: {} and processing date: {}", shiftCafeMappingId, processingDate);
        
        return shiftCoveragePlanRepository.findByShiftCafeMappingIdAndProcessingDate(shiftCafeMappingId, processingDate);
    }

    @Override
    public void deleteShiftCoveragePlan(Integer shiftCafeMappingId) {
        log.info("Deleting shift coverage plan with ID: {}", shiftCafeMappingId);
        
        ShiftCoveragePlan shiftCoveragePlan = getShiftCoveragePlanById(shiftCafeMappingId);
        shiftCoveragePlanRepository.delete(shiftCoveragePlan);
    }

    @Override
    public void deactivateShiftCoveragePlan(Integer shiftCafeMappingId) {
        log.info("Deactivating shift coverage plan with ID: {}", shiftCafeMappingId);
        
        ShiftCoveragePlan shiftCoveragePlan = getShiftCoveragePlanById(shiftCafeMappingId);
        shiftCoveragePlan.setStatus("INACTIVE");
        shiftCoveragePlan.setUpdationTime(LocalDateTime.now());
        
        shiftCoveragePlanRepository.save(shiftCoveragePlan);
    }
} 