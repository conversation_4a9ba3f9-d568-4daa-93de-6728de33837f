package com.stpl.tech.attendance.domain;

import java.sql.Timestamp;

/**
 * Concrete implementation of EmpShiftMapping for Reladomo
 * This class extends the generated abstract class and provides the concrete implementation
 */
public class EmpShiftMapping extends EmpShiftMappingAbstract {
    
    public EmpShiftMapping(Timestamp businessDate, Timestamp processingDate) {
        super(businessDate, processingDate);
    }
    
    public EmpShiftMapping(Timestamp businessDate) {
        super(businessDate);
    }
}
