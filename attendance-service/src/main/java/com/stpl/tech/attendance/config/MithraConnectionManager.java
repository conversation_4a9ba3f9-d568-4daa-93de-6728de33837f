package com.stpl.tech.attendance.config;

import com.gs.fw.common.mithra.connectionmanager.SourcelessConnectionManager;
import com.gs.fw.common.mithra.databasetype.DatabaseType;
import com.gs.fw.common.mithra.bulkloader.BulkLoader;
import com.gs.fw.common.mithra.bulkloader.BulkLoaderException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import java.sql.Connection;
import java.sql.DriverManager;
import java.util.TimeZone;
import com.gs.fw.common.mithra.databasetype.GenericDatabaseType;

@Component
public class MithraConnectionManager implements SourcelessConnectionManager, ApplicationContextAware {

    private static ApplicationContext applicationContext;

    @Value("${spring.datasource.url}")
    private String url;

    @Value("${spring.datasource.username}")
    private String username;

    @Value("${spring.datasource.password}")
    private String password;

    /**
     * Static getInstance method required by <PERSON><PERSON><PERSON>
     * This method returns the Spring-managed instance
     */
    public static MithraConnectionManager getInstance() {
        if (applicationContext == null) {
            throw new RuntimeException("ApplicationContext not initialized. Cannot get MithraConnectionManager instance.");
        }
        return applicationContext.getBean(MithraConnectionManager.class);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        MithraConnectionManager.applicationContext = applicationContext;
    }

    @Override
    public Connection getConnection() {
        try {
            return DriverManager.getConnection(url, username, password);
        } catch (Exception e) {
            throw new RuntimeException("Failed to get DB connection", e);
        }
    }

    @Override
    public DatabaseType getDatabaseType() {
        return GenericDatabaseType.getInstance();
    }

    @Override
    public TimeZone getDatabaseTimeZone() {
        return TimeZone.getDefault();
    }

    @Override
    public BulkLoader createBulkLoader() throws BulkLoaderException {
        throw new UnsupportedOperationException("Bulk loading not implemented");
    }

    @Override
    public String getDatabaseIdentifier() {
        return "default";
    }
}