# Rostering API Response Examples

## 1. GET /dashboard/cafe-live-dashboard

**Description**: Get cafe live dashboard data based on employee ID

**Request**: 
```
GET /api/v1/roster/dashboard/cafe-live-dashboard
Headers: X-Employee-Id: 12345
```

**Response** (`CafeLiveDashboardResponseDTO`):
```json
{
  "dashboardView": {
    "cafeDashboardView": {
      "cafeDashboard": true,
      "shiftDashboard": true,
      "employeeDashboard": false
    },
    "date": {
      "startDate": "2024-01-01",
      "endDate": "2024-01-31"
    },
    "cafeDashboard": {
      "actual": 120,
      "ideal": 150
    },
    "shifts": [
      {
        "shiftId": "shift_001",
        "startDate": "2024-01-01T09:00:00Z",
        "endDate": "2024-01-01T17:00:00Z",
        "numberOfEmployees": 5,
        "shiftName": "Morning Shift"
      },
      {
        "shiftId": "shift_002",
        "startDate": "2024-01-01T17:00:00Z",
        "endDate": "2024-01-02T01:00:00Z",
        "numberOfEmployees": 3,
        "shiftName": "Evening Shift"
      }
    ]
  }
}
```

## 2. GET /shifts/shift-employees

**Description**: Get employees assigned to shifts

**Request**: 
```
GET /api/v1/roster/shifts/shift-employees?shiftId=1&unitId=5
```

**Response** (`ShiftEmployeesResponseDTO`):
```json
{
  "shifts": [
    {
      "shiftId": "shift_001",
      "employeeId": "emp_001",
      "name": "John Doe",
      "role": "Barista"
    },
    {
      "shiftId": "shift_001",
      "employeeId": "emp_002",
      "name": "John Wick",
      "role": "Barista"
    },
    {
      "shiftId": "shift_002",
      "employeeId": "emp_003",
      "name": "Jane Smith",
      "role": "Supervisor"
    }
  ]
}
```

## 3. GET /shifts/emp-shift-data/{empId}

**Description**: Get shift assignments for a specific employee

**Request**: 
```
GET /api/v1/roster/shifts/emp-shift-data/123?startDate=2024-01-01T00:00:00&endDate=2024-01-31T23:59:59
```

**Response** (`EmployeeShiftDataResponseDTO`):
```json
{
  "employeeId": "emp_123",
  "shifts": [
    {
      "shiftId": "shift_001",
      "shiftName": "Morning Shift",
      "startTime": "09:00:00",
      "endTime": "17:00:00",
      "date": "2024-01-01"
    },
    {
      "shiftId": "shift_002",
      "shiftName": "Evening Shift",
      "startTime": "17:00:00",
      "endTime": "01:00:00",
      "date": "2024-01-02"
    }
  ]
}
```

## Key Features:

### 1. **Employee-Based Dashboard**
- The cafe live dashboard now fetches data based on the logged-in employee ID
- Shows shifts and employee counts related to that employee's access level
- Provides dashboard view flags for different dashboard types

### 2. **Formatted IDs**
- `shiftId`: Formatted as "shift_001", "shift_002", etc.
- `employeeId`: Formatted as "emp_001", "emp_002", etc.
- Uses zero-padded 3-digit format for consistency

### 3. **Time Formatting**
- **Dashboard dates**: ISO format with Z suffix (e.g., "2024-01-01T09:00:00Z")
- **Employee shift times**: Time only format (e.g., "09:00:00", "17:00:00")
- **Employee shift dates**: Date only format (e.g., "2024-01-01")

### 4. **Data Relationships**
- Dashboard shows shifts related to the employee's access
- Shift employees shows all employees in specific shifts
- Employee shift data shows all shifts assigned to a specific employee

### 5. **Flexible Filtering**
- Dashboard: Based on employee ID (from authentication)
- Shift employees: Optional shiftId and unitId filters
- Employee shifts: Optional date range filters

## Implementation Notes:

1. **Employee ID Extraction**: The dashboard endpoint uses `getLoggedInUser(request)` to get the current employee ID
2. **Data Aggregation**: The service layer aggregates data from multiple tables (SHIFTS, EMP_SHIFT_MAPPING, SHIFT_CAFE_MAPPING)
3. **Cache Integration**: Uses existing cache services for employee and unit data
4. **Error Handling**: Comprehensive error handling with meaningful error messages
5. **Performance**: Optimized queries with proper indexing and pagination support
