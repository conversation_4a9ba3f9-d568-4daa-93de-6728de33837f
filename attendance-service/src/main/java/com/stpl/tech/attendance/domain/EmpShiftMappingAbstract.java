package com.stpl.tech.attendance.domain;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.io.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.gs.fw.common.mithra.*;
import com.gs.fw.common.mithra.attribute.*;
import com.gs.fw.common.mithra.util.*;
import com.gs.fw.common.mithra.notification.*;
import com.gs.fw.common.mithra.notification.listener.*;
import com.gs.fw.common.mithra.list.cursor.Cursor;
import com.gs.fw.common.mithra.bulkloader.*;
import com.gs.fw.common.mithra.MithraObject;
import com.gs.fw.common.mithra.MithraDataObject;
import com.gs.fw.common.mithra.cache.Cache;
import com.gs.fw.common.mithra.finder.*;
import com.gs.fw.common.mithra.extractor.*;
import com.gs.fw.common.mithra.list.*;
import com.gs.fw.common.mithra.behavior.state.DatedPersistenceState;
import com.gs.fw.common.mithra.transaction.MithraObjectPersister;
import java.util.HashSet;
import java.util.Arrays;
/**
* This file was automatically generated using Mithra 17.0.1. Please do not modify it.
* Add custom logic to its subclass instead.
*/
// Generated from templates/datedreadonly/Abstract.jsp
public abstract class EmpShiftMappingAbstract
implements MithraDatedObject, Serializable
{
	protected EmpShiftMappingData currentData;
	protected byte dataVersion;
	protected byte persistenceState = DatedPersistenceState.IN_MEMORY_NON_TRANSACTIONAL;
	protected Timestamp businessDate;
	protected Timestamp processingDate;
	public EmpShiftMappingAbstract(Timestamp businessDate
	, Timestamp processingDate
	)
	{
		this.businessDate = TimestampPool.getInstance().getOrAddToCache(businessDate, EmpShiftMappingFinder.isFullCache());
		this.processingDate = TimestampPool.getInstance().getOrAddToCache(processingDate, EmpShiftMappingFinder.isFullCache());
	}

	public EmpShiftMappingAbstract(Timestamp businessDate)
	{
		this(businessDate, java.sql.Timestamp.valueOf("9999-12-01 23:59:00.000"));
	}

	protected Object zGetLock()
	{
		return this;
	}

	public void zSetNonTxPersistenceState(int state)
	{
		this.persistenceState = (byte) state;
	}

	public boolean isDeletedOrMarkForDeletion()
	{
		return this.persistenceState == DatedPersistenceState.DELETED || this.persistenceState == DatedPersistenceState.PERSISTED_NON_TRANSACTIONAL && zIsDataDeleted();
	}

	public void setFromEmpShiftMappingData( EmpShiftMappingData data )
	{
		synchronized(this.zGetLock())
		{
			this.currentData = data;
			this.dataVersion = data.zGetDataVersion();
			this.persistenceState = DatedPersistenceState.PERSISTED_NON_TRANSACTIONAL;
		}
	}

	protected void zSetFromEmpShiftMappingData( EmpShiftMappingData data )
	{
		this.setFromEmpShiftMappingData(data);
	}

	public void zSetCurrentData(MithraDataObject data)
	{
		this.setFromEmpShiftMappingData( (EmpShiftMappingData) data );
	}

	public EmpShiftMapping getNonPersistentCopy() throws MithraBusinessException
	{
		MithraDataObject newData = this.currentData.copy();
		Timestamp[] asOfAttributes = new Timestamp[2];
		asOfAttributes[0] = this.businessDate;
		asOfAttributes[1] = this.processingDate;
		EmpShiftMappingAbstract copy = (EmpShiftMappingAbstract)
		EmpShiftMappingFinder.getMithraObjectPortal().getMithraDatedObjectFactory().createObject(newData, asOfAttributes);
		copy.persistenceState = DatedPersistenceState.IN_MEMORY_NON_TRANSACTIONAL;
		return (EmpShiftMapping)copy;
	}

	public MithraDataObject zGetCurrentData()
	{
		return this.currentData;
	}

	public MithraDataObject zGetCurrentOrTransactionalData()
	{
		MithraDataObject result = this.currentData;
		if (result == null)
		{
			synchronized(this.zGetLock())
			{
				result = this.currentData;
			}
		}

		return result;
	}

	public void zReindexAndSetDataIfChanged(MithraDataObject data, Cache cache)
	{
		throw new RuntimeException("should never be called");
	}

	public void zSetData(MithraDataObject data, Object optional)
	{
		throw new RuntimeException("should never be called");
	}

	public MithraDataObject zRefreshWithLock(boolean lock)
	{
		EmpShiftMappingData data = (EmpShiftMappingData) EmpShiftMappingFinder.getMithraObjectPortal().refreshDatedObject(this, lock);
		if (data == null)
		{
			throw new MithraDeletedException("EmpShiftMapping has been deleted: " + this.zGetCurrentData().zGetPrintablePrimaryKey());
		}

		return data;
	}

	public Cache zGetCache()
	{
		return EmpShiftMappingFinder.getMithraObjectPortal().getCache();
	}

	public EmpShiftMappingData zGetCurrentDataWithCheck()
	{
		EmpShiftMappingData current = currentData;
		if (current == null && persistenceState == DatedPersistenceState.IN_MEMORY_NON_TRANSACTIONAL)
		{
			return zGetOrInitializeInMemoryData();
		}

		if (persistenceState != DatedPersistenceState.IN_MEMORY_NON_TRANSACTIONAL)
		{
			boolean refresh = current.zGetDataVersion() < 0;
			if (!refresh && dataVersion != current.zGetDataVersion())
			{
				refresh = zCheckForRefresh(current);
			}

			if (refresh)
			{
				current = zRefreshData(current);
			}
		}

		return current;
	}

	private EmpShiftMappingData zRefreshData(EmpShiftMappingData current)
	{
		EmpShiftMappingData newData = (EmpShiftMappingData) this.zGetCache().refreshOutsideTransaction(this, current);
		if (newData == null)
		{
			throw new MithraDeletedException("EmpShiftMapping has been deleted: " + current.zGetPrintablePrimaryKey());
		}

		synchronized(this.zGetLock())
		{
			if (currentData == current)
			{
				currentData = newData;
			}
		}

		return newData;
	}

	private boolean zCheckForRefresh(EmpShiftMappingData current)
	{
		boolean refresh = !EmpShiftMappingFinder.businessDate().dataMatches(current, businessDate);
		if (!refresh)
		{
			refresh = !EmpShiftMappingFinder.processingDate().dataMatches(current, processingDate);
		}

		return refresh;
	}

	private EmpShiftMappingData zGetOrInitializeInMemoryData()
	{
		EmpShiftMappingData current;
		synchronized(this.zGetLock())
		{
			if (currentData == null)
			{
				currentData = new EmpShiftMappingData();
			}

			current = this.currentData;
		}

		return current;
	}

	private boolean zIsDataDeleted()
	{
		EmpShiftMappingData current = currentData;
		boolean refresh = current.zGetDataVersion() < 0;
		if (!refresh && dataVersion != current.zGetDataVersion())
		{
			refresh = zCheckForRefresh(current);
		}

		if (refresh)
		{
			EmpShiftMappingData newData = (EmpShiftMappingData) this.zGetCache().refreshOutsideTransaction(this, current);
			if (newData == null) return true;
		}

		return false;
	}

	public boolean isBusinessDateFromNull()
	{
		return this.zGetCurrentDataWithCheck().isBusinessDateFromNull();
	}

	public Timestamp getBusinessDateFrom()
	{
		EmpShiftMappingData data = this.zGetCurrentDataWithCheck();
		return data.getBusinessDateFrom();
	}

	public void setBusinessDateFrom(Timestamp newValue)
	{
		this.ensureSetAttributeAllowed();
		this.zGetCurrentDataWithCheck().setBusinessDateFrom(newValue);
	}

	public boolean isBusinessDateToNull()
	{
		return this.zGetCurrentDataWithCheck().isBusinessDateToNull();
	}

	public Timestamp getBusinessDateTo()
	{
		EmpShiftMappingData data = this.zGetCurrentDataWithCheck();
		return data.getBusinessDateTo();
	}

	public void setBusinessDateTo(Timestamp newValue)
	{
		this.ensureSetAttributeAllowed();
		this.zGetCurrentDataWithCheck().setBusinessDateTo(newValue);
	}

	public boolean isCreatedByNull()
	{
		return this.zGetCurrentDataWithCheck().isCreatedByNull();
	}

	public String getCreatedBy()
	{
		EmpShiftMappingData data = this.zGetCurrentDataWithCheck();
		return data.getCreatedBy();
	}

	public void setCreatedBy(String newValue)
	{
		this.ensureSetAttributeAllowed();
		this.zGetCurrentDataWithCheck().setCreatedBy(newValue);
	}

	public boolean isCreationTimeNull()
	{
		return this.zGetCurrentDataWithCheck().isCreationTimeNull();
	}

	public Timestamp getCreationTime()
	{
		EmpShiftMappingData data = this.zGetCurrentDataWithCheck();
		return data.getCreationTime();
	}

	public void setCreationTime(Timestamp newValue)
	{
		this.ensureSetAttributeAllowed();
		this.zGetCurrentDataWithCheck().setCreationTime(newValue);
	}

	public boolean isEmpIdNull()
	{
		return this.zGetCurrentDataWithCheck().isEmpIdNull();
	}

	public int getEmpId()
	{
		EmpShiftMappingData data = this.zGetCurrentDataWithCheck();
		if (data.isEmpIdNull())
		MithraNullPrimitiveException.throwNew("empId", data);
		return data.getEmpId();
	}

	public void setEmpId(int newValue)
	{
		this.ensureSetAttributeAllowed();
		this.zGetCurrentDataWithCheck().setEmpId(newValue);
	}

	public boolean isExpectedEndDateNull()
	{
		return this.zGetCurrentDataWithCheck().isExpectedEndDateNull();
	}

	public Timestamp getExpectedEndDate()
	{
		EmpShiftMappingData data = this.zGetCurrentDataWithCheck();
		return data.getExpectedEndDate();
	}

	public void setExpectedEndDate(Timestamp newValue)
	{
		this.ensureSetAttributeAllowed();
		this.zGetCurrentDataWithCheck().setExpectedEndDate(newValue);
	}

	public boolean isExpectedStartDateNull()
	{
		return this.zGetCurrentDataWithCheck().isExpectedStartDateNull();
	}

	public Timestamp getExpectedStartDate()
	{
		EmpShiftMappingData data = this.zGetCurrentDataWithCheck();
		return data.getExpectedStartDate();
	}

	public void setExpectedStartDate(Timestamp newValue)
	{
		this.ensureSetAttributeAllowed();
		this.zGetCurrentDataWithCheck().setExpectedStartDate(newValue);
	}

	public boolean isIdNull()
	{
		return this.currentData.isIdNull();
	}

	public int getId()
	{
		EmpShiftMappingData data = this.currentData;
		return data.getId();
	}

	public void setId(int newValue)
	{
		this.ensureSetAttributeAllowed();
		this.zGetCurrentDataWithCheck().setId(newValue);
	}

	public boolean isProcessingDateFromNull()
	{
		return this.zGetCurrentDataWithCheck().isProcessingDateFromNull();
	}

	public Timestamp getProcessingDateFrom()
	{
		EmpShiftMappingData data = this.zGetCurrentDataWithCheck();
		return data.getProcessingDateFrom();
	}

	public void setProcessingDateFrom(Timestamp newValue)
	{
		this.ensureSetAttributeAllowed();
		this.zGetCurrentDataWithCheck().setProcessingDateFrom(newValue);
	}

	public boolean isProcessingDateToNull()
	{
		return this.zGetCurrentDataWithCheck().isProcessingDateToNull();
	}

	public Timestamp getProcessingDateTo()
	{
		EmpShiftMappingData data = this.zGetCurrentDataWithCheck();
		return data.getProcessingDateTo();
	}

	public void setProcessingDateTo(Timestamp newValue)
	{
		this.ensureSetAttributeAllowed();
		this.zGetCurrentDataWithCheck().setProcessingDateTo(newValue);
	}

	public boolean isShiftIdNull()
	{
		return this.zGetCurrentDataWithCheck().isShiftIdNull();
	}

	public int getShiftId()
	{
		EmpShiftMappingData data = this.zGetCurrentDataWithCheck();
		if (data.isShiftIdNull())
		MithraNullPrimitiveException.throwNew("shiftId", data);
		return data.getShiftId();
	}

	public void setShiftId(int newValue)
	{
		this.ensureSetAttributeAllowed();
		this.zGetCurrentDataWithCheck().setShiftId(newValue);
	}

	public boolean isStatusNull()
	{
		return this.zGetCurrentDataWithCheck().isStatusNull();
	}

	public String getStatus()
	{
		EmpShiftMappingData data = this.zGetCurrentDataWithCheck();
		return data.getStatus();
	}

	public void setStatus(String newValue)
	{
		this.ensureSetAttributeAllowed();
		this.zGetCurrentDataWithCheck().setStatus(newValue);
	}

	public boolean isUpdatedByNull()
	{
		return this.zGetCurrentDataWithCheck().isUpdatedByNull();
	}

	public String getUpdatedBy()
	{
		EmpShiftMappingData data = this.zGetCurrentDataWithCheck();
		return data.getUpdatedBy();
	}

	public void setUpdatedBy(String newValue)
	{
		this.ensureSetAttributeAllowed();
		this.zGetCurrentDataWithCheck().setUpdatedBy(newValue);
	}

	public boolean isUpdationTimeNull()
	{
		return this.zGetCurrentDataWithCheck().isUpdationTimeNull();
	}

	public Timestamp getUpdationTime()
	{
		EmpShiftMappingData data = this.zGetCurrentDataWithCheck();
		return data.getUpdationTime();
	}

	public void setUpdationTime(Timestamp newValue)
	{
		this.ensureSetAttributeAllowed();
		this.zGetCurrentDataWithCheck().setUpdationTime(newValue);
	}

	protected void ensureSetAttributeAllowed()
	{
		if (this.persistenceState != DatedPersistenceState.IN_MEMORY_NON_TRANSACTIONAL)
		{
			throw new MithraBusinessException("Cannot set an attribute of a persisted read-only object");
		}
	}

	public Timestamp getBusinessDate()
	{
		return this.businessDate;
	}

	public Timestamp getProcessingDate()
	{
		return this.processingDate;
	}

	public void setShiftIdNull()
	{
		this.ensureSetAttributeAllowed();
		this.zGetCurrentDataWithCheck().setShiftIdNull();
	}

	public void setEmpIdNull()
	{
		this.ensureSetAttributeAllowed();
		this.zGetCurrentDataWithCheck().setEmpIdNull();
	}

	public void zWriteDataClassName(ObjectOutput out) throws IOException
	{
	}

	public void zSerializeFullData(ObjectOutput out) throws IOException
	{
		EmpShiftMappingData data = ((EmpShiftMappingData)this.zGetCurrentDataWithCheck());
		data.zSerializeFullData(out);
		MithraTimestamp.writeTimezoneInsensitiveTimestampWithInfinity(out, businessDate, java.sql.Timestamp.valueOf("9999-12-01 23:59:00.000"));
		MithraTimestamp.writeTimezoneInsensitiveTimestampWithInfinity(out, processingDate, java.sql.Timestamp.valueOf("9999-12-01 23:59:00.000"));
	}

	public void zSerializePrimaryKey(ObjectOutput out) throws IOException
	{
		EmpShiftMappingData data = ((EmpShiftMappingData)this.zGetCurrentOrTransactionalData());
		data.zSerializePrimaryKey(out);
		MithraTimestamp.writeTimezoneInsensitiveTimestampWithInfinity(out, businessDate, java.sql.Timestamp.valueOf("9999-12-01 23:59:00.000"));
		MithraTimestamp.writeTimezoneInsensitiveTimestampWithInfinity(out, processingDate, java.sql.Timestamp.valueOf("9999-12-01 23:59:00.000"));
	}

	public void zSerializeFullTxData(ObjectOutput out) throws IOException
	{
		this.zSerializeFullData(out);
	}

	private void writeObject(java.io.ObjectOutputStream out)
	throws IOException
	{
		boolean writeFullData = (persistenceState == DatedPersistenceState.IN_MEMORY_NON_TRANSACTIONAL);
		MithraDataObject dataToWrite = this.zGetCurrentDataWithCheck();
		out.writeBoolean(writeFullData);
		if (writeFullData)
		{
			dataToWrite.zSerializeFullData(out);
			dataToWrite.zSerializeRelationships(out);
		}
		else
		{
			dataToWrite.zSerializePrimaryKey(out);
		}

		MithraTimestamp.writeTimezoneInsensitiveTimestampWithInfinity(out, businessDate, java.sql.Timestamp.valueOf("9999-12-01 23:59:00.000"));
		MithraTimestamp.writeTimezoneInsensitiveTimestampWithInfinity(out, processingDate, java.sql.Timestamp.valueOf("9999-12-01 23:59:00.000"));
	}

	private void readObject(java.io.ObjectInputStream in)
	throws IOException, ClassNotFoundException
	{
		boolean fullData = in.readBoolean();
		this.currentData = new EmpShiftMappingData();
		if (fullData)
		{
			currentData.zDeserializeFullData(in);
			persistenceState = DatedPersistenceState.IN_MEMORY_NON_TRANSACTIONAL;
			this.currentData.zDeserializeRelationships(in);
		}
		else
		{
			currentData.zDeserializePrimaryKey(in);
			persistenceState = DatedPersistenceState.PERSISTED_NON_TRANSACTIONAL;
		}

		businessDate = TimestampPool.getInstance().getOrAddToCache(MithraTimestamp.readTimezoneInsensitiveTimestampWithInfinity(in, java.sql.Timestamp.valueOf("9999-12-01 23:59:00.000")), EmpShiftMappingFinder.isFullCache());
		processingDate = TimestampPool.getInstance().getOrAddToCache(MithraTimestamp.readTimezoneInsensitiveTimestampWithInfinity(in, java.sql.Timestamp.valueOf("9999-12-01 23:59:00.000")), EmpShiftMappingFinder.isFullCache());
	}

	public Object readResolve() throws ObjectStreamException
	{
		if (persistenceState == DatedPersistenceState.PERSISTED_NON_TRANSACTIONAL)
		{
			Operation op = EmpShiftMappingFinder.businessDate().eq(businessDate);
			op = op.and(EmpShiftMappingFinder.processingDate().eq(processingDate));
			op = op.and(EmpShiftMappingFinder.id().eq(currentData.getId()));
			return EmpShiftMappingFinder.findOne(op);
		}

		return this;
	}

	public void zMarkDirty()
	{
	}

	public boolean zDataMatches(Object data, Timestamp[] asOfDates)
	{
		EmpShiftMappingData localData = (EmpShiftMappingData) data;
		MithraDataObject thisData = this.zGetCurrentOrTransactionalData();
		return thisData != null && localData.hasSamePrimaryKeyIgnoringAsOfAttributes(thisData)
		&& this.businessDate.equals(asOfDates[0])
		&& this.processingDate.equals(asOfDates[1])
		;
	}

	public MithraObjectPortal zGetPortal()
	{
		return EmpShiftMappingFinder.getMithraObjectPortal();
	}
}
