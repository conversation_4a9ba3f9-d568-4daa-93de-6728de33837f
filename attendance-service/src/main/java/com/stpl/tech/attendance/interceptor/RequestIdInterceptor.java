package com.stpl.tech.attendance.interceptor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import java.util.UUID;

/**
 * Interceptor to generate and track unique request IDs for logging purposes.
 * Adds request ID to MDC (Mapped Diagnostic Context) so it appears in all log entries.
 */
@Slf4j
@Component
public class RequestIdInterceptor implements HandlerInterceptor {

    private static final String REQUEST_ID_HEADER = "X-Request-ID";
    private static final String REQUEST_ID_MDC_KEY = "requestId";
    private static final String USER_ID_MDC_KEY = "userId";
    private static final String UNIT_ID_MDC_KEY = "unitId";
    private static final String SESSION_ID_MDC_KEY = "sessionId";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        // Generate or extract request ID
        String requestId = extractOrGenerateRequestId(request);
        
        // Add request ID to MDC for logging
        MDC.put(REQUEST_ID_MDC_KEY, requestId);
        
        // Add request ID to response headers for client tracking
        response.setHeader(REQUEST_ID_HEADER, requestId);
        
        // Log request start
        log.info("Request started - Method: {}, URI: {}, RemoteAddr: {}", 
                request.getMethod(), 
                request.getRequestURI(), 
                getClientIpAddress(request));
        
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        // Log request completion with status
       /* log.info("Request completed - Method: {}, URI: {}, Status: {}, Duration: {}ms",
                request.getMethod(), 
                request.getRequestURI(), 
                response.getStatus(),
                System.currentTimeMillis() - (long)request.getAttribute("startTime"));
        */
        // Clear MDC context
        MDC.clear();
    }

    /**
     * Extract request ID from header or generate a new one
     */
    private String extractOrGenerateRequestId(HttpServletRequest request) {
        String requestId = request.getHeader(REQUEST_ID_HEADER);
        if (requestId == null || requestId.trim().isEmpty()) {
            requestId = generateRequestId();
        }
        return requestId;
    }

    /**
     * Generate a unique request ID
     */
    private String generateRequestId() {
        return UUID.randomUUID().toString().replace("-", "").substring(0, 16);
    }

    /**
     * Get client IP address from request
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }

    /**
     * Set user context in MDC (called from JwtAuthInterceptor)
     */
    public static void setUserContext(Integer userId, Integer unitId, String sessionId) {
        if (userId != null) {
            MDC.put(USER_ID_MDC_KEY, userId.toString());
        }
        if (unitId != null) {
            MDC.put(UNIT_ID_MDC_KEY, unitId.toString());
        }
        if (sessionId != null) {
            MDC.put(SESSION_ID_MDC_KEY, sessionId);
        }
    }

    /**
     * Get current request ID from MDC
     */
    public static String getCurrentRequestId() {
        return MDC.get(REQUEST_ID_MDC_KEY);
    }
} 