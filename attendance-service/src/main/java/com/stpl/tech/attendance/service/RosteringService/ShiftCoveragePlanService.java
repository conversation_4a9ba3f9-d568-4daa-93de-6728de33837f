package com.stpl.tech.attendance.service.RosteringService;

import com.stpl.tech.attendance.entity.RosteringEntity.ShiftCoveragePlan;

import java.time.LocalDateTime;
import java.util.List;

public interface ShiftCoveragePlanService {

    /**
     * Create a new shift coverage plan
     */
    ShiftCoveragePlan createShiftCoveragePlan(ShiftCoveragePlan shiftCoveragePlan);

    /**
     * Update an existing shift coverage plan
     */
    ShiftCoveragePlan updateShiftCoveragePlan(ShiftCoveragePlan shiftCoveragePlan);

    /**
     * Get shift coverage plan by ID
     */
    ShiftCoveragePlan getShiftCoveragePlanById(Integer shiftCafeMappingId);

    /**
     * Get all shift coverage plans by shift cafe mapping ID
     */
    List<ShiftCoveragePlan> getShiftCoveragePlansByShiftCafeMappingId(Integer shiftCafeMappingId);

    /**
     * Get shift coverage plans by day
     */
    List<ShiftCoveragePlan> getShiftCoveragePlansByDay(Integer day);

    /**
     * Get active shift coverage plans by shift cafe mapping ID
     */
    List<ShiftCoveragePlan> getActiveShiftCoveragePlansByShiftCafeMappingId(Integer shiftCafeMappingId);

    /**
     * Get shift coverage plan by shift cafe mapping ID and day
     */
    ShiftCoveragePlan getShiftCoveragePlanByShiftCafeMappingIdAndDay(Integer shiftCafeMappingId, Integer day);

    /**
     * Get shift coverage plans within business date range
     */
    List<ShiftCoveragePlan> getShiftCoveragePlansByShiftCafeMappingIdAndBusinessDate(Integer shiftCafeMappingId, LocalDateTime businessDate);

    /**
     * Get shift coverage plans within processing date range
     */
    List<ShiftCoveragePlan> getShiftCoveragePlansByShiftCafeMappingIdAndProcessingDate(Integer shiftCafeMappingId, LocalDateTime processingDate);

    /**
     * Delete shift coverage plan by ID
     */
    void deleteShiftCoveragePlan(Integer shiftCafeMappingId);

    /**
     * Deactivate shift coverage plan (soft delete)
     */
    void deactivateShiftCoveragePlan(Integer shiftCafeMappingId);
} 