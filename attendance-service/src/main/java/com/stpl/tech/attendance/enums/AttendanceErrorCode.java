package com.stpl.tech.attendance.enums;

import lombok.Getter;

@Getter
public enum AttendanceErrorCode {
    EMPLOYEE_NOT_FOUND("ATT_001", "Employee not found"),
    NOT_ELIGIBLE_FOR_ATTENDANCE("ATT_002", "Employee is not eligible for attendance at this unit"),
    DUPLICATE_ATTENDANCE("ATT_003", "Duplicate attendance record found"),
    INVALID_ATTENDANCE_TYPE("ATT_004", "Invalid attendance type provided"),
    ATTENDANCE_NOT_FOUND("ATT_005", "Attendance record not found"),
    INVALID_WORK_HOURS("ATT_006", "Invalid work hours provided"),
    INVALID_LOCATION("ATT_007", "Invalid location data provided"),
    ATTENDANCE_PROCESSING_ERROR("ATT_008", "Failed to process attendance record"),
    INVALID_ATTENDANCE_STATUS("ATT_009", "Invalid attendance status provided"),
    ATTENDANCE_UPDATE_ERROR("ATT_010", "Failed to update attendance record"),
    INVALID_ATTENDANCE_DATA("ATT_011", "Invalid attendance data provided"),
    ATTENDANCE_DELETION_ERROR("ATT_012", "Failed to delete attendance record"),
    INVALID_ATTENDANCE_METADATA("ATT_013", "Invalid attendance metadata provided"),
    ATTENDANCE_EXPORT_ERROR("ATT_014", "Failed to export attendance records"),
    ATTENDANCE_IMPORT_ERROR("ATT_015", "Failed to import attendance records"),
    INVALID_PUNCH_TYPE("ATT_016", "Invalid punch type provided"),
    INACTIVE_EMPLOYEE("ATT_017", "Employee is inactive"),
    BIOMETRIC_NOT_REGISTERED("ATT_018","Biometric not registered for employee"),
    SHIFT_COVERAGE_PLAN_NOT_FOUND("ATT_019", "Shift coverage plan not found");

    private final String code;
    private final String defaultMessage;

    AttendanceErrorCode(String code, String defaultMessage) {
        this.code = code;
        this.defaultMessage = defaultMessage;
    }
} 