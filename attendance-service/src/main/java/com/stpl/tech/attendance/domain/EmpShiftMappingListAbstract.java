package com.stpl.tech.attendance.domain;
import java.util.*;
import com.gs.fw.common.mithra.MithraList;
import com.gs.fw.common.mithra.MithraManager;
import com.gs.fw.common.mithra.MithraManagerProvider;
import com.gs.fw.common.mithra.finder.*;
import com.gs.fw.common.mithra.list.*;
import com.gs.fw.common.mithra.list.merge.TopLevelMergeOptions;
import com.gs.fw.finder.OrderBy;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.io.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.gs.fw.common.mithra.*;
import com.gs.fw.common.mithra.attribute.*;
import com.gs.fw.common.mithra.util.*;
import com.gs.fw.common.mithra.notification.*;
import com.gs.fw.common.mithra.notification.listener.*;
import com.gs.fw.common.mithra.list.cursor.Cursor;
import com.gs.fw.common.mithra.bulkloader.*;
/**
* This file was automatically generated using Mithra 17.0.1. Please do not modify it.
* Add custom logic to its subclass instead.
*/
// Generated from templates/transactional/ListAbstract.jsp
public class EmpShiftMappingListAbstract extends DelegatingList<EmpShiftMapping> implements MithraList<EmpShiftMapping>
{
	public EmpShiftMappingListAbstract()
	{
		super();
		this.setDelegated(AbstractNonOperationBasedList.DEFAULT);
		AbstractNonOperationBasedList.DEFAULT.init(this);
	}

	public EmpShiftMappingListAbstract(int initialSize)
	{
		super();
		this.setDelegated(AbstractNonOperationBasedList.DEFAULT);
		AbstractNonOperationBasedList.DEFAULT.init(this, initialSize);
	}

	public EmpShiftMappingListAbstract(Collection c)
	{
		super();
		this.setDelegated(AbstractNonOperationBasedList.DEFAULT);
		AbstractNonOperationBasedList.DEFAULT.init(this, c);
	}

	public EmpShiftMappingListAbstract(com.gs.fw.finder.Operation operation)
	{
		super(operation);
		this.setDelegated(AbstractOperationBasedList.DEFAULT);
	}

	public EmpShiftMapping[] elements()
	{
		EmpShiftMapping[] result = new EmpShiftMapping[size()];
		this.getDelegated().toArray(this, result);
		return result;
	}

	public EmpShiftMappingList intersection(EmpShiftMappingList another)
	{
		return (EmpShiftMappingList)super.intersection(another);
	}

	public EmpShiftMapping getEmpShiftMappingAt(int index)
	{
		return (EmpShiftMapping)this.get(index);
	}

	public MithraObjectPortal getMithraObjectPortal()
	{
		return EmpShiftMappingFinder.getMithraObjectPortal();
	}

	public EmpShiftMappingList getNonPersistentCopy()
	{
		EmpShiftMappingList result = new EmpShiftMappingList();
		zCopyNonPersistentInto(result);
		return result;
	}

	public EmpShiftMappingList asAdhoc()
	{
		return (EmpShiftMappingList) super.asAdhoc();
	}

	public MithraList getNonPersistentGenericCopy()
	{
		return this.getNonPersistentCopy();
	}
}
