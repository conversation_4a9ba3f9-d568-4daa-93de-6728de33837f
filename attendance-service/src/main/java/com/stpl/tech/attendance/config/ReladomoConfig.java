package com.stpl.tech.attendance.config;

import com.gs.fw.common.mithra.MithraManager;
import com.gs.fw.common.mithra.MithraManagerProvider;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.io.InputStream;

/**
 * Configuration class for Reladomo initialization
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
public class ReladomoConfig {

    private final MithraConnectionManager connectionManager;

    @PostConstruct
    public void initializeReladomo() {
        try {
            log.info("Initializing Reladomo...");

            // Initialize MithraManager
            MithraManager mithraManager = MithraManagerProvider.getMithraManager();
//            mithraManager.setJtaTransactionManagerProvider(null);
            // Load runtime configuration
            ClassPathResource resource = new ClassPathResource("MithraRuntimeConfig.xml");
            try (InputStream configStream = resource.getInputStream()) {
                mithraManager.readConfiguration(configStream);
                log.info("Reladomo configuration loaded successfully");
            }

            // Initialize the object portal
            mithraManager.fullyInitialize();
            log.info("Reladomo fully initialized");

        } catch (Exception e) {
            log.error("Failed to initialize Reladomo", e);
            throw new RuntimeException("Reladomo initialization failed", e);
        }
    }

    @PreDestroy
    public void shutdownReladomo() {
        try {
            log.info("Shutting down Reladomo...");
            // Reladomo doesn't have a shutdown method in this version
            // The cleanup is handled automatically by the JVM
            log.info("Reladomo shutdown completed");
        } catch (Exception e) {
            log.error("Error during Reladomo shutdown", e);
        }
    }

    @Bean
    public MithraManager mithraManager() {
        return MithraManagerProvider.getMithraManager();
    }
}
