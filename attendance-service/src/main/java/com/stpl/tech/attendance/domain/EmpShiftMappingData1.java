package com.stpl.tech.attendance.domain;
import java.util.*;
import java.sql.Timestamp;
import java.sql.Date;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.io.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.gs.fw.common.mithra.*;
import com.gs.fw.common.mithra.attribute.*;
import com.gs.fw.common.mithra.util.*;
import com.gs.fw.common.mithra.notification.*;
import com.gs.fw.common.mithra.notification.listener.*;
import com.gs.fw.common.mithra.list.cursor.Cursor;
import com.gs.fw.common.mithra.bulkloader.*;
import com.gs.fw.common.mithra.finder.PrintablePreparedStatement;
import com.gs.fw.common.mithra.finder.RelatedFinder;
import com.gs.fw.common.mithra.cache.offheap.MithraOffHeapDataObject;
import com.gs.fw.common.mithra.cache.offheap.OffHeapDataStorage;
/**
* This file was automatically generated using Mithra 17.0.1. Please do not modify it.
* Add custom logic to its subclass instead.
*/
public class EmpShiftMappingData
implements MithraDataObject
{
	private byte _dataVersion;
	private byte isNullBits0 = 0;
	private Timestamp businessDateFrom;
	private Timestamp businessDateTo;
	private String createdBy;
	private Timestamp creationTime;
	private int empId;
	private Timestamp expectedEndDate;
	private Timestamp expectedStartDate;
	private int id;
	private Timestamp processingDateFrom;
	private Timestamp processingDateTo;
	private int shiftId;
	private String status;
	private String updatedBy;
	private Timestamp updationTime;
	public boolean isBusinessDateFromNull()
	{
		return this.getBusinessDateFrom() == null;
	}

	public boolean isBusinessDateToNull()
	{
		return this.getBusinessDateTo() == null;
	}

	public boolean isCreatedByNull()
	{
		return this.getCreatedBy() == null;
	}

	public boolean isCreationTimeNull()
	{
		return this.getCreationTime() == null;
	}

	public boolean isExpectedEndDateNull()
	{
		return this.getExpectedEndDate() == null;
	}

	public boolean isExpectedStartDateNull()
	{
		return this.getExpectedStartDate() == null;
	}

	public boolean isIdNull()
	{
		return false;
	}

	public boolean isProcessingDateFromNull()
	{
		return this.getProcessingDateFrom() == null;
	}

	public boolean isProcessingDateToNull()
	{
		return this.getProcessingDateTo() == null;
	}

	public boolean isStatusNull()
	{
		return this.getStatus() == null;
	}

	public boolean isUpdatedByNull()
	{
		return this.getUpdatedBy() == null;
	}

	public boolean isUpdationTimeNull()
	{
		return this.getUpdationTime() == null;
	}

	public boolean isShiftIdNull()
	{
		return (isNullBits0 & 1) != 0 ;
	}

	public boolean isEmpIdNull()
	{
		return (isNullBits0 & 1 << 1) != 0 ;
	}

	public void zSerializeFullData(ObjectOutput out) throws IOException
	{
		zWriteNullBits(out);
		MithraTimestamp.writeTimezoneInsensitiveTimestamp(out, this.businessDateFrom);
		MithraTimestamp.writeTimezoneInsensitiveTimestampWithInfinity(out, this.businessDateTo, EmpShiftMappingFinder.businessDate().getInfinityDate());
		out.writeObject(this.createdBy);
		MithraTimestamp.writeTimezoneInsensitiveTimestamp(out, this.creationTime);
		out.writeInt(this.empId);
		MithraTimestamp.writeTimezoneInsensitiveTimestamp(out, this.expectedEndDate);
		MithraTimestamp.writeTimezoneInsensitiveTimestamp(out, this.expectedStartDate);
		out.writeInt(this.id);
		MithraTimestamp.writeTimezoneInsensitiveTimestamp(out, this.processingDateFrom);
		MithraTimestamp.writeTimezoneInsensitiveTimestampWithInfinity(out, this.processingDateTo, EmpShiftMappingFinder.processingDate().getInfinityDate());
		out.writeInt(this.shiftId);
		out.writeObject(this.status);
		out.writeObject(this.updatedBy);
		MithraTimestamp.writeTimezoneInsensitiveTimestamp(out, this.updationTime);
	}

	private void zWriteNullBits(ObjectOutput out) throws IOException
	{
		out.writeByte(this.isNullBits0);
	}

	public Timestamp getBusinessDateFrom()
	{
		return this.businessDateFrom;
	}

	public long zGetBusinessDateFromAsLong()
	{
		if (businessDateFrom == null) return TimestampPool.OFF_HEAP_NULL;
		return businessDateFrom.getTime();
	}

	public void setBusinessDateFrom(Timestamp value)
	{
		this.businessDateFrom = TimestampPool.getInstance().getOrAddToCache(value, EmpShiftMappingFinder.isFullCache(), EmpShiftMappingFinder.isOffHeap());
	}

	public void setBusinessDateFromNull()
	{
		this.setBusinessDateFrom(null);
	}

	public Timestamp getBusinessDateTo()
	{
		return this.businessDateTo;
	}

	public long zGetBusinessDateToAsLong()
	{
		if (businessDateTo == null) return TimestampPool.OFF_HEAP_NULL;
		return businessDateTo.getTime();
	}

	public void setBusinessDateTo(Timestamp value)
	{
		if (!value.equals(this.businessDateTo))
		{
			zIncrementDataVersion();
		}

		this.businessDateTo = TimestampPool.getInstance().getOrAddToCache(value, EmpShiftMappingFinder.isFullCache(), EmpShiftMappingFinder.isOffHeap());
	}

	public void setBusinessDateToNull()
	{
		throw new RuntimeException("should never be called");
	}

	public String getCreatedBy()
	{
		return this.createdBy;
	}

	public int zGetCreatedByAsInt()
	{
		return StringPool.getInstance().getOffHeapAddressWithoutAdding(createdBy);
	}

	public void setCreatedBy(String value)
	{
		this.createdBy = StringPool.getInstance().getOrAddToCache(value, EmpShiftMappingFinder.isFullCache());
	}

	public void setCreatedByNull()
	{
		this.setCreatedBy(null);
	}

	public Timestamp getCreationTime()
	{
		return this.creationTime;
	}

	public long zGetCreationTimeAsLong()
	{
		if (creationTime == null) return TimestampPool.OFF_HEAP_NULL;
		return creationTime.getTime();
	}

	public void setCreationTime(Timestamp value)
	{
		this.creationTime = TimestampPool.getInstance().getOrAddToCache(value, EmpShiftMappingFinder.isFullCache(), EmpShiftMappingFinder.isOffHeap());
	}

	public void setCreationTimeNull()
	{
		this.setCreationTime(null);
	}

	public int getEmpId()
	{
		return this.empId;
	}

	public void setEmpId(int value)
	{
		this.empId = value;
		isNullBits0 = (byte)((int)isNullBits0 & ~( 1 << 1));
	}

	public Timestamp getExpectedEndDate()
	{
		return this.expectedEndDate;
	}

	public long zGetExpectedEndDateAsLong()
	{
		if (expectedEndDate == null) return TimestampPool.OFF_HEAP_NULL;
		return expectedEndDate.getTime();
	}

	public void setExpectedEndDate(Timestamp value)
	{
		this.expectedEndDate = TimestampPool.getInstance().getOrAddToCache(value, EmpShiftMappingFinder.isFullCache(), EmpShiftMappingFinder.isOffHeap());
	}

	public void setExpectedEndDateNull()
	{
		this.setExpectedEndDate(null);
	}

	public Timestamp getExpectedStartDate()
	{
		return this.expectedStartDate;
	}

	public long zGetExpectedStartDateAsLong()
	{
		if (expectedStartDate == null) return TimestampPool.OFF_HEAP_NULL;
		return expectedStartDate.getTime();
	}

	public void setExpectedStartDate(Timestamp value)
	{
		this.expectedStartDate = TimestampPool.getInstance().getOrAddToCache(value, EmpShiftMappingFinder.isFullCache(), EmpShiftMappingFinder.isOffHeap());
	}

	public void setExpectedStartDateNull()
	{
		this.setExpectedStartDate(null);
	}

	public int getId()
	{
		return this.id;
	}

	public void setId(int value)
	{
		this.id = value;
	}

	public void setIdNull()
	{
		throw new RuntimeException("should never be called");
	}

	public Timestamp getProcessingDateFrom()
	{
		return this.processingDateFrom;
	}

	public long zGetProcessingDateFromAsLong()
	{
		if (processingDateFrom == null) return TimestampPool.OFF_HEAP_NULL;
		return processingDateFrom.getTime();
	}

	public void setProcessingDateFrom(Timestamp value)
	{
		this.processingDateFrom = TimestampPool.getInstance().getOrAddToCache(value, EmpShiftMappingFinder.isFullCache(), EmpShiftMappingFinder.isOffHeap());
	}

	public void setProcessingDateFromNull()
	{
		this.setProcessingDateFrom(null);
	}

	public Timestamp getProcessingDateTo()
	{
		return this.processingDateTo;
	}

	public long zGetProcessingDateToAsLong()
	{
		if (processingDateTo == null) return TimestampPool.OFF_HEAP_NULL;
		return processingDateTo.getTime();
	}

	public void setProcessingDateTo(Timestamp value)
	{
		if (!value.equals(this.processingDateTo))
		{
			zIncrementDataVersion();
		}

		this.processingDateTo = TimestampPool.getInstance().getOrAddToCache(value, EmpShiftMappingFinder.isFullCache(), EmpShiftMappingFinder.isOffHeap());
	}

	public void setProcessingDateToNull()
	{
		throw new RuntimeException("should never be called");
	}

	public int getShiftId()
	{
		return this.shiftId;
	}

	public void setShiftId(int value)
	{
		this.shiftId = value;
		isNullBits0 = (byte)((int)isNullBits0 & ~( 1));
	}

	public String getStatus()
	{
		return this.status;
	}

	public int zGetStatusAsInt()
	{
		return StringPool.getInstance().getOffHeapAddressWithoutAdding(status);
	}

	public void setStatus(String value)
	{
		this.status = StringPool.getInstance().getOrAddToCache(value, EmpShiftMappingFinder.isFullCache());
	}

	public void setStatusNull()
	{
		this.setStatus(null);
	}

	public String getUpdatedBy()
	{
		return this.updatedBy;
	}

	public int zGetUpdatedByAsInt()
	{
		return StringPool.getInstance().getOffHeapAddressWithoutAdding(updatedBy);
	}

	public void setUpdatedBy(String value)
	{
		this.updatedBy = StringPool.getInstance().getOrAddToCache(value, EmpShiftMappingFinder.isFullCache());
	}

	public void setUpdatedByNull()
	{
		this.setUpdatedBy(null);
	}

	public Timestamp getUpdationTime()
	{
		return this.updationTime;
	}

	public long zGetUpdationTimeAsLong()
	{
		if (updationTime == null) return TimestampPool.OFF_HEAP_NULL;
		return updationTime.getTime();
	}

	public void setUpdationTime(Timestamp value)
	{
		this.updationTime = TimestampPool.getInstance().getOrAddToCache(value, EmpShiftMappingFinder.isFullCache(), EmpShiftMappingFinder.isOffHeap());
	}

	public void setUpdationTimeNull()
	{
		this.setUpdationTime(null);
	}

	public void setShiftIdNull()
	{
		isNullBits0 = (byte)((int)isNullBits0 | 1);
	}

	public void setEmpIdNull()
	{
		isNullBits0 = (byte)((int)isNullBits0 | 1 << 1);
	}

	public byte zGetIsNullBits0()
	{
		return this.isNullBits0;
	}

	public void zSetIsNullBits0(byte newValue)
	{
		this.isNullBits0 = newValue;
	}

	public byte zGetDataVersion() 
	{
		return _dataVersion; 
	}

	public void zSetDataVersion(byte version) 
	{
		this._dataVersion = version; 
	}

	public void zIncrementDataVersion()
	{
		_dataVersion++;
		if (_dataVersion > 120) _dataVersion = (byte) 0;
	}

	protected void copyInto(EmpShiftMappingData copy, boolean withRelationships)
	{
		copy.isNullBits0 = this.isNullBits0;
		copy.businessDateFrom = this.businessDateFrom;
		copy.businessDateTo = this.businessDateTo;
		copy.createdBy = this.createdBy;
		copy.creationTime = this.creationTime;
		copy.empId = this.empId;
		copy.expectedEndDate = this.expectedEndDate;
		copy.expectedStartDate = this.expectedStartDate;
		copy.id = this.id;
		copy.processingDateFrom = this.processingDateFrom;
		copy.processingDateTo = this.processingDateTo;
		copy.shiftId = this.shiftId;
		copy.status = this.status;
		copy.updatedBy = this.updatedBy;
		copy.updationTime = this.updationTime;
		if (withRelationships)
		{
		}
	}

	public void zDeserializeFullData(ObjectInput in) throws IOException, ClassNotFoundException
	{
		this.isNullBits0 = in.readByte();
		this.businessDateFrom = TimestampPool.getInstance().getOrAddToCache(MithraTimestamp.readTimezoneInsensitiveTimestamp(in), EmpShiftMappingFinder.isFullCache(), EmpShiftMappingFinder.isOffHeap());
		this.businessDateTo = TimestampPool.getInstance().getOrAddToCache(MithraTimestamp.readTimezoneInsensitiveTimestampWithInfinity(in, EmpShiftMappingFinder.businessDate().getInfinityDate()), EmpShiftMappingFinder.isFullCache(), EmpShiftMappingFinder.isOffHeap());
		this.createdBy = StringPool.getInstance().getOrAddToCache((String)in.readObject(), EmpShiftMappingFinder.isFullCache());
		this.creationTime = TimestampPool.getInstance().getOrAddToCache(MithraTimestamp.readTimezoneInsensitiveTimestamp(in), EmpShiftMappingFinder.isFullCache(), EmpShiftMappingFinder.isOffHeap());
		this.empId = in.readInt();
		this.expectedEndDate = TimestampPool.getInstance().getOrAddToCache(MithraTimestamp.readTimezoneInsensitiveTimestamp(in), EmpShiftMappingFinder.isFullCache(), EmpShiftMappingFinder.isOffHeap());
		this.expectedStartDate = TimestampPool.getInstance().getOrAddToCache(MithraTimestamp.readTimezoneInsensitiveTimestamp(in), EmpShiftMappingFinder.isFullCache(), EmpShiftMappingFinder.isOffHeap());
		this.id = in.readInt();
		this.processingDateFrom = TimestampPool.getInstance().getOrAddToCache(MithraTimestamp.readTimezoneInsensitiveTimestamp(in), EmpShiftMappingFinder.isFullCache(), EmpShiftMappingFinder.isOffHeap());
		this.processingDateTo = TimestampPool.getInstance().getOrAddToCache(MithraTimestamp.readTimezoneInsensitiveTimestampWithInfinity(in, EmpShiftMappingFinder.processingDate().getInfinityDate()), EmpShiftMappingFinder.isFullCache(), EmpShiftMappingFinder.isOffHeap());
		this.shiftId = in.readInt();
		this.status = StringPool.getInstance().getOrAddToCache((String)in.readObject(), EmpShiftMappingFinder.isFullCache());
		this.updatedBy = StringPool.getInstance().getOrAddToCache((String)in.readObject(), EmpShiftMappingFinder.isFullCache());
		this.updationTime = TimestampPool.getInstance().getOrAddToCache(MithraTimestamp.readTimezoneInsensitiveTimestamp(in), EmpShiftMappingFinder.isFullCache(), EmpShiftMappingFinder.isOffHeap());
	}

	public boolean hasSamePrimaryKeyIgnoringAsOfAttributes(MithraDataObject other)
	{
		if (this == other) return true;
		final EmpShiftMappingData otherData = (EmpShiftMappingData) other;
		if (getId() != otherData.getId())
		{
			return false;
		}

		return true;
	}

	public void zSerializePrimaryKey(ObjectOutput out) throws IOException
	{
		MithraTimestamp.writeTimezoneInsensitiveTimestamp(out, this.businessDateFrom);
		MithraTimestamp.writeTimezoneInsensitiveTimestampWithInfinity(out, this.businessDateTo, EmpShiftMappingFinder.businessDate().getInfinityDate());
		out.writeInt(this.id);
		MithraTimestamp.writeTimezoneInsensitiveTimestamp(out, this.processingDateFrom);
		MithraTimestamp.writeTimezoneInsensitiveTimestampWithInfinity(out, this.processingDateTo, EmpShiftMappingFinder.processingDate().getInfinityDate());
	}

	public void zDeserializePrimaryKey(ObjectInput in) throws IOException, ClassNotFoundException
	{
		this.businessDateFrom = TimestampPool.getInstance().getOrAddToCache(MithraTimestamp.readTimezoneInsensitiveTimestamp(in), EmpShiftMappingFinder.isFullCache(), EmpShiftMappingFinder.isOffHeap());
		this.businessDateTo = TimestampPool.getInstance().getOrAddToCache(MithraTimestamp.readTimezoneInsensitiveTimestampWithInfinity(in, EmpShiftMappingFinder.businessDate().getInfinityDate()), EmpShiftMappingFinder.isFullCache(), EmpShiftMappingFinder.isOffHeap());
		this.id = in.readInt();
		this.processingDateFrom = TimestampPool.getInstance().getOrAddToCache(MithraTimestamp.readTimezoneInsensitiveTimestamp(in), EmpShiftMappingFinder.isFullCache(), EmpShiftMappingFinder.isOffHeap());
		this.processingDateTo = TimestampPool.getInstance().getOrAddToCache(MithraTimestamp.readTimezoneInsensitiveTimestampWithInfinity(in, EmpShiftMappingFinder.processingDate().getInfinityDate()), EmpShiftMappingFinder.isFullCache(), EmpShiftMappingFinder.isOffHeap());
	}

	public void clearRelationships()
	{
		clearAllDirectRefs();
	}

	public void clearAllDirectRefs()
	{
	}

	public void zSerializeRelationships(ObjectOutputStream out) throws IOException
	{
	}

	public void zDeserializeRelationships(ObjectInputStream in) throws IOException, ClassNotFoundException
	{
	}

	public MithraOffHeapDataObject zCopyOffHeap()
	{
		throw new RuntimeException("off heap no supported");
	}

	public void copyNonPkAttributes(MithraDataObject newData)
	{
		final EmpShiftMappingData empShiftMappingData = (EmpShiftMappingData) newData;
		this.setBusinessDateFrom(empShiftMappingData.getBusinessDateFrom());
		this.setBusinessDateTo(empShiftMappingData.getBusinessDateTo());
		this.setCreatedBy(empShiftMappingData.getCreatedBy());
		this.setCreationTime(empShiftMappingData.getCreationTime());
		this.setEmpId(empShiftMappingData.getEmpId());
		if (empShiftMappingData.isEmpIdNull()) this.setEmpIdNull();
		this.setExpectedEndDate(empShiftMappingData.getExpectedEndDate());
		this.setExpectedStartDate(empShiftMappingData.getExpectedStartDate());
		this.setProcessingDateFrom(empShiftMappingData.getProcessingDateFrom());
		this.setProcessingDateTo(empShiftMappingData.getProcessingDateTo());
		this.setShiftId(empShiftMappingData.getShiftId());
		if (empShiftMappingData.isShiftIdNull()) this.setShiftIdNull();
		this.setStatus(empShiftMappingData.getStatus());
		this.setUpdatedBy(empShiftMappingData.getUpdatedBy());
		this.setUpdationTime(empShiftMappingData.getUpdationTime());
	}

	public boolean zNonPrimaryKeyAttributesChanged(MithraDataObject newData)
	{
		return this.zNonPrimaryKeyAttributesChanged(newData, 0.0);
	}

	public boolean zNonPrimaryKeyAttributesChanged(MithraDataObject newData, double toleranceForFloatingPointFields)
	{
		final EmpShiftMappingData other = (EmpShiftMappingData) newData;
		if (!isCreatedByNull() ? !getCreatedBy().equals(other.getCreatedBy()) : !other.isCreatedByNull())
		{
			return true;
		}

		if (!isCreationTimeNull() ? !getCreationTime().equals(other.getCreationTime()) : !other.isCreationTimeNull())
		{
			return true;
		}

		if ( isEmpIdNull() != other.isEmpIdNull() || getEmpId() != other.getEmpId())
		{
			return true;
		}

		if (!isExpectedEndDateNull() ? !getExpectedEndDate().equals(other.getExpectedEndDate()) : !other.isExpectedEndDateNull())
		{
			return true;
		}

		if (!isExpectedStartDateNull() ? !getExpectedStartDate().equals(other.getExpectedStartDate()) : !other.isExpectedStartDateNull())
		{
			return true;
		}

		if ( isShiftIdNull() != other.isShiftIdNull() || getShiftId() != other.getShiftId())
		{
			return true;
		}

		if (!isStatusNull() ? !getStatus().equals(other.getStatus()) : !other.isStatusNull())
		{
			return true;
		}

		if (!isUpdatedByNull() ? !getUpdatedBy().equals(other.getUpdatedBy()) : !other.isUpdatedByNull())
		{
			return true;
		}

		if (!isUpdationTimeNull() ? !getUpdationTime().equals(other.getUpdationTime()) : !other.isUpdationTimeNull())
		{
			return true;
		}

		return false;
	}

	public MithraDataObject copy()
	{
		EmpShiftMappingData copy = new EmpShiftMappingData();
		this.copyInto(copy, true);
		return copy;
	}

	public MithraDataObject copy(boolean withRelationships)
	{
		EmpShiftMappingData copy = new EmpShiftMappingData();
		this.copyInto(copy, withRelationships);
		return copy;
	}

	public String zGetPrintablePrimaryKey()
	{
		String result = "";
		result += "businessDateFrom: "+(isBusinessDateFromNull() ? "null" : PrintablePreparedStatement.timestampFormat.print(zGetBusinessDateFromAsLong()))+ " / ";
		result += "businessDateTo: "+(isBusinessDateToNull() ? "null" : PrintablePreparedStatement.timestampFormat.print(zGetBusinessDateToAsLong()))+ " / ";
		result += "id: "+(""+getId())+ " / ";
		result += "processingDateFrom: "+(isProcessingDateFromNull() ? "null" : PrintablePreparedStatement.timestampFormat.print(zGetProcessingDateFromAsLong()))+ " / ";
		result += "processingDateTo: "+(isProcessingDateToNull() ? "null" : PrintablePreparedStatement.timestampFormat.print(zGetProcessingDateToAsLong()))+ " / ";
		return result;
	}

	public boolean zAsOfAttributesFromEquals(MithraDataObject other)
	{
		boolean result = true;
		EmpShiftMappingData otherData = (EmpShiftMappingData) other;
		result &= zGetBusinessDateFromAsLong() == otherData.zGetBusinessDateFromAsLong();
		result &= zGetProcessingDateFromAsLong() == otherData.zGetProcessingDateFromAsLong();
		return result;
	}

	public boolean zAsOfAttributesChanged(MithraDataObject other)
	{
		EmpShiftMappingData otherData = (EmpShiftMappingData) other;
		if (zGetBusinessDateToAsLong() != otherData.zGetBusinessDateToAsLong())
		{
			return true;
		}

		if (zGetBusinessDateFromAsLong() != otherData.zGetBusinessDateFromAsLong())
		{
			return true;
		}

		if (zGetProcessingDateToAsLong() != otherData.zGetProcessingDateToAsLong())
		{
			return true;
		}

		if (zGetProcessingDateFromAsLong() != otherData.zGetProcessingDateFromAsLong())
		{
			return true;
		}

		return false;
	}

	public void zWriteDataClassName(ObjectOutput out) throws IOException
	{
	}

	public String zReadDataClassName(ObjectInput in) throws IOException, ClassNotFoundException
	{
		return "com.stpl.tech.attendance.domain.EmpShiftMappingData";
	}

	public boolean changed(MithraDataObject newData)
	{
		if(zNonPrimaryKeyAttributesChanged(newData))
		{
			return true;
		}
		else
		{
			return zAsOfAttributesChanged(newData);
		}
	}

	public boolean zHasSameNullPrimaryKeyAttributes(MithraDataObject newData)
	{
		return true;
	}

	public MithraObjectPortal zGetMithraObjectPortal(int hierarchyDepth)
	{
		return EmpShiftMappingFinder.getMithraObjectPortal();
	}

	public MithraObjectPortal zGetMithraObjectPortal()
	{
		return EmpShiftMappingFinder.getMithraObjectPortal();
	}

	public Number zGetIdentityValue()
	{
		return null;
	}

	public boolean zHasIdentity()
	{
		return false;
	}

	public void zSetIdentity(Number identityValue)
	{
	}

	public String zGetSerializationClassName()
	{
		return "com.stpl.tech.attendance.domain.EmpShiftMappingData";
	}
}
