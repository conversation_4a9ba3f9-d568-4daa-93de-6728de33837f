<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN" monitorInterval="30">
    <Properties>
        <!-- Use system property or default to the specified deployment path -->
        <Property name="LOGS">${sys:LOG_PATH:-/home/<USER>/deployment/logs/attendance-service}</Property>
        <Property name="APP_NAME">${spring:spring.application.name:-attendance-service}</Property>
        
        <!-- Log patterns -->
        <Property name="CONSOLE_PATTERN">%style{%d{ISO8601}}{bright,black} %highlight{%-5level} [%style{%t}{bright,blue}] [%style{%X{requestId}}{bright,cyan}] [%style{user:%X{userId}}{bright,yellow}] [%style{unit:%X{unitId}}{bright,green}] %style{%C{1}}{bright,yellow}: %msg%n%throwable</Property>
        <Property name="FILE_PATTERN">%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %-5level [%X{requestId}] [user:%X{userId}] [unit:%X{unitId}] [session:%X{sessionId}] %logger{36} - %msg%n</Property>
    </Properties>

    <Appenders>
        <!-- Console Appender -->
        <Console name="Console" target="SYSTEM_OUT" follow="true">
            <PatternLayout pattern="${CONSOLE_PATTERN}"/>
        </Console>

        <!-- Rolling File Appender for main logs -->
        <RollingFile name="RollingFile" 
                     fileName="${LOGS}/${APP_NAME}.log"
                     filePattern="${LOGS}/archived/${APP_NAME}-%d{yyyy-MM-dd}-%i.log.gz">
            <PatternLayout pattern="${FILE_PATTERN}"/>
            <Policies>
                <TimeBasedTriggeringPolicy />
                <SizeBasedTriggeringPolicy size="10MB"/>
            </Policies>
            <DefaultRolloverStrategy max="30" fileIndex="max">
                <Delete basePath="${LOGS}/archived" maxDepth="1">
                    <IfFileName glob="${APP_NAME}-*.log.gz"/>
                    <IfLastModified age="30d"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingFile>

        <!-- Rolling File Appender for error logs -->
        <RollingFile name="ErrorFile" 
                     fileName="${LOGS}/${APP_NAME}-error.log"
                     filePattern="${LOGS}/archived/${APP_NAME}-error-%d{yyyy-MM-dd}-%i.log.gz">
            <PatternLayout pattern="${FILE_PATTERN}"/>
            <Filters>
                <ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <Policies>
                <TimeBasedTriggeringPolicy />
                <SizeBasedTriggeringPolicy size="10MB"/>
            </Policies>
            <DefaultRolloverStrategy max="30" fileIndex="max">
                <Delete basePath="${LOGS}/archived" maxDepth="1">
                    <IfFileName glob="${APP_NAME}-error-*.log.gz"/>
                    <IfLastModified age="30d"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingFile>
    </Appenders>

    <Loggers>
        <!-- Application logger -->
        <Logger name="com.stpl.tech.attendance" level="info" additivity="false">
            <AppenderRef ref="RollingFile"/>
            <AppenderRef ref="ErrorFile"/>
            <AppenderRef ref="Console"/>
        </Logger>

        <!-- Reduce noise from Spring and other frameworks -->
        <Logger name="org.springframework" level="warn" additivity="false">
            <AppenderRef ref="RollingFile"/>
            <AppenderRef ref="ErrorFile"/>
            <AppenderRef ref="Console"/>
        </Logger>
        
        <Logger name="org.hibernate" level="warn" additivity="false">
            <AppenderRef ref="RollingFile"/>
            <AppenderRef ref="ErrorFile"/>
            <AppenderRef ref="Console"/>
        </Logger>
        
        <Logger name="com.zaxxer.hikari" level="warn" additivity="false">
            <AppenderRef ref="RollingFile"/>
            <AppenderRef ref="ErrorFile"/>
            <AppenderRef ref="Console"/>
        </Logger>

        <!-- Root logger -->
        <Root level="info">
            <AppenderRef ref="RollingFile"/>
            <AppenderRef ref="ErrorFile"/>
            <AppenderRef ref="Console"/>
        </Root>
    </Loggers>
</Configuration> 