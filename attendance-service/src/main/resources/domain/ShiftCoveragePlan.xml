<MithraObject>
    <PackageName>com.stpl.tech.attendance.domain</PackageName>
    <ClassName>ShiftCoveragePlan</ClassName>
    <DefaultTable>SHIFT_COVERAGE_PLAN</DefaultTable>

    <!-- Bitemporal Configuration -->
    <AsOfAttribute name="businessDate" fromColumnName="BUSINESS_FROM" toColumnName="BUSINESS_TO"
                   toIsInclusive="false"
                   isProcessingDate="false"
                   infinityDate="[java.sql.Timestamp.valueOf('9999-12-01 23:59:00.000')]"
                   futureExpiringRowsExist="true"/>
    <AsOfAttribute name="processingDate" fromColumnName="PROCESSING_FROM" toColumnName="PROCESSING_TO"
                   toIsInclusive="false"
                   isProcessingDate="true"
                   infinityDate="[java.sql.Timestamp.valueOf('9999-12-01 23:59:00.000')]"
                   defaultIfNotSpecified="[java.sql.Timestamp.valueOf('9999-12-01 23:59:00.000')]"/>

    <!-- Primary Key -->
    <Attribute name="shiftCafeMappingId" javaType="Integer" primaryKey="true" identity="true" columnName="SHIFT_CAFE_MAPPING_ID"/>

    <!-- Business Attributes -->
    <Attribute name="day" javaType="Integer" columnName="DAY"/>
    <Attribute name="idealCount" javaType="Integer" columnName="IDEAL_COUNT"/>
    <Attribute name="status" javaType="String" columnName="STATUS"/>

    <!-- Audit Attributes -->
    <Attribute name="createdBy" javaType="String" columnName="CREATED_BY"/>
    <Attribute name="creationTime" javaType="Timestamp" columnName="CREATION_TIME"/>
    <Attribute name="updatedBy" javaType="String" columnName="UPDATED_BY"/>
    <Attribute name="updationTime" javaType="Timestamp" columnName="UPDATION_TIME"/>

    <!-- Indexes for Performance -->
<!--    <Index name="IDX_SHIFT_COVERAGE_MAPPING_ID">-->
<!--        <Attribute>shiftCafeMappingId</Attribute>-->
<!--    </Index>-->
<!--    <Index name="IDX_SHIFT_COVERAGE_DAY">-->
<!--        <Attribute>day</Attribute>-->
<!--    </Index>-->
</MithraObject> 