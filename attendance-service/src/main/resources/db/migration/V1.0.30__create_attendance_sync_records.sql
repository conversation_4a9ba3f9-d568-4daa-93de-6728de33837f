-- Create attendance_sync_records table
CREATE TABLE attendance_sync_records (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    employee_id INT NOT NULL,
    attendance_record_id BIGINT NOT NULL,
    external_system_id VARCHAR(255),
    sync_status VARCHAR(20) NOT NULL,
    last_synced_srno BIGINT,
    sync_attempts INT DEFAULT 0,
    last_sync_attempt TIMESTAMP,
    error_message TEXT,
    external_response TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_attendance_record_id (attendance_record_id),
    INDEX idx_employee_id (employee_id),
    INDEX idx_sync_status (sync_status),
    INDEX idx_created_at (created_at),
    UNIQUE KEY uk_attendance_record_id (attendance_record_id)
);

-- Add biometric_device_id column to attendance_records table if it doesn't exist
ALTER TABLE attendance_records 
ADD COLUMN biometric_device_id VARCHAR(255) COMMENT 'Format: terminalId_unitType_unitId' 
AFTER biometric_id; 