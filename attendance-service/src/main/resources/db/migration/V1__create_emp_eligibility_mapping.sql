-- Create EMP_ELIGIBILITY_MAPPING table
CREATE TABLE EMP_ELIGIBILITY_MAPPING (
    ID BIGINT AUTO_INCREMENT PRIMARY KEY,
    EMP_ID VARCHAR(50) NOT NULL,
    VALUE VARCHAR(100) NOT NULL,
    MAPPING_TYPE VARCHAR(20) NOT NULL,
    ELIGIBILITY_TYPE VARCHAR(20) NOT NULL,
    STATUS VARCHAR(20) NOT NULL,
    CREATED_BY VARCHAR(50),
    UPDATED_BY VARCHAR(50),
    CREATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UPDATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    CONSTRAINT chk_mapping_type CHECK (MAPPING_TYPE IN ('UNIT', 'CITY', 'REGION')),
    CONSTRAINT chk_eligibility_type CHECK (ELIGIBILITY_TYPE IN ('ATTENDANCE', 'APPROVAL')),
    CONSTRAINT chk_status CHECK (STATUS IN ('ACTIVE', 'IN_ACTIVE'))
);

-- <PERSON>reate indexes for better query performance
CREATE INDEX idx_emp_eligibility_emp_id ON EMP_ELIGIBILITY_MAPPING(EMP_ID);
CREATE INDEX idx_emp_eligibility_value ON EMP_ELIGIBILITY_MAPPING(VALUE);
CREATE INDEX idx_emp_eligibility_mapping_type ON EMP_ELIGIBILITY_MAPPING(MAPPING_TYPE);
CREATE INDEX idx_emp_eligibility_eligibility_type ON EMP_ELIGIBILITY_MAPPING(ELIGIBILITY_TYPE);
CREATE INDEX idx_emp_eligibility_status ON EMP_ELIGIBILITY_MAPPING(STATUS);

-- Create composite index for common queries
CREATE INDEX idx_emp_eligibility_composite ON EMP_ELIGIBILITY_MAPPING(EMP_ID, ELIGIBILITY_TYPE, STATUS);
CREATE INDEX idx_emp_eligibility_value_composite ON EMP_ELIGIBILITY_MAPPING(VALUE, MAPPING_TYPE, ELIGIBILITY_TYPE, STATUS);

-- Add comments to the table and columns
COMMENT ON TABLE EMP_ELIGIBILITY_MAPPING IS 'Stores employee eligibility mappings for attendance and approvals';
COMMENT ON COLUMN EMP_ELIGIBILITY_MAPPING.EMP_ID IS 'Employee ID';
COMMENT ON COLUMN EMP_ELIGIBILITY_MAPPING.VALUE IS 'Value of the mapping (e.g., unit ID, city name, region name)';
COMMENT ON COLUMN EMP_ELIGIBILITY_MAPPING.MAPPING_TYPE IS 'Type of mapping (UNIT, CITY, REGION)';
COMMENT ON COLUMN EMP_ELIGIBILITY_MAPPING.ELIGIBILITY_TYPE IS 'Type of eligibility (ATTENDANCE, APPROVAL)';
COMMENT ON COLUMN EMP_ELIGIBILITY_MAPPING.STATUS IS 'Status of the mapping (ACTIVE, IN_ACTIVE)';
COMMENT ON COLUMN EMP_ELIGIBILITY_MAPPING.CREATED_BY IS 'User who created the record';
COMMENT ON COLUMN EMP_ELIGIBILITY_MAPPING.UPDATED_BY IS 'User who last updated the record';
COMMENT ON COLUMN EMP_ELIGIBILITY_MAPPING.CREATED_AT IS 'Timestamp when the record was created';
COMMENT ON COLUMN EMP_ELIGIBILITY_MAPPING.UPDATED_AT IS 'Timestamp when the record was last updated'; 