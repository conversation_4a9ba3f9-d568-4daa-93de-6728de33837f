CREATE TABLE BIOMETRIC_TEMPLATE (
    ID BIGINT AUTO_INCREMENT PRIMARY KEY,
    EMP_ID INT NOT NULL,
    UNIT_ID INT NOT NULL,
    DEVICE_TYPE VARCHAR(50) NOT NULL,
    DEVICE_ID VARCHAR(100) NOT NULL,
    OS_VERSION VARCHAR(50),
    LOCATION VARCHAR(100),
    BIOMETRIC_ID VARCHAR(100) NOT NULL,
    USER_ID VARCHAR(100) NOT NULL,
    STATUS VARCHAR(20) NOT NULL,
    CREATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UPDATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    CONSTRAINT FK_BIOMETRIC_EMP FOREIGN KEY (EMP_ID) REFERENCES EMPLOYEE(EMP_ID),
    CONSTRAINT FK_BIOMETRIC_UNIT FOREIGN KEY (UNIT_ID) REFERENCES UNIT(UNIT_ID)
);

CREATE INDEX IDX_BIOMETRIC_EMP_ID ON BIOMETRIC_TEMPLATE(EMP_ID);
CREATE INDEX IDX_BIOMETRIC_UNIT_ID ON BIOMETRIC_TEMPLATE(UNIT_ID);
CREATE INDEX IDX_BIOMETRIC_STATUS ON BIOMETRIC_TEMPLATE(STATUS);
CREATE INDEX IDX_BIOMETRIC_DEVICE_ID ON BIOMETRIC_TEMPLATE(DEVICE_ID); 