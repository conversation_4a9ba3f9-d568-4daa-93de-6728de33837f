-- Create SHIFTS table
CREATE TABLE SHIFTS (
    SHIFT_ID INT AUTO_INCREMENT PRIMARY KEY,
    SHIFT_NAME VARCHAR(100) NOT NULL,
    START_TIME TIMESTAMP NOT NULL,
    END_TIME TIMESTAMP NOT NULL,
    STATUS VARCHAR(45) NOT NULL DEFAULT 'ACTIVE',
    CREATED_BY VARCHAR(100),
    CREATION_TIME TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UPDATED_BY VARCHAR(100),
    UPDATION_TIME TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- <PERSON>reate indexes for better performance
CREATE INDEX idx_shifts_status ON SHIFTS(STATUS);
CREATE INDEX idx_shifts_name ON SHIFTS(SHIFT_NAME);
CREATE INDEX idx_shifts_start_time ON SHIFTS(START_TIME);
CREATE INDEX idx_shifts_end_time ON SHIFTS(END_TIME);

-- Add unique constraint for active shift names
CREATE UNIQUE INDEX idx_shifts_name_status ON SHIFTS(SHIFT_NAME, STATUS);

-- Add comments
COMMENT ON TABLE SHIFTS IS 'Stores shift definitions with start and end times';
COMMENT ON COLUMN SHIFTS.SHIFT_ID IS 'Primary key for shift';
COMMENT ON COLUMN SHIFTS.SHIFT_NAME IS 'Name of the shift (e.g., Morning, Evening, Night)';
COMMENT ON COLUMN SHIFTS.START_TIME IS 'Shift start time';
COMMENT ON COLUMN SHIFTS.END_TIME IS 'Shift end time';
COMMENT ON COLUMN SHIFTS.STATUS IS 'Status of the shift (ACTIVE, INACTIVE)';
COMMENT ON COLUMN SHIFTS.CREATED_BY IS 'User who created the shift';
COMMENT ON COLUMN SHIFTS.CREATION_TIME IS 'Timestamp when shift was created';
COMMENT ON COLUMN SHIFTS.UPDATED_BY IS 'User who last updated the shift';
COMMENT ON COLUMN SHIFTS.UPDATION_TIME IS 'Timestamp when shift was last updated';
