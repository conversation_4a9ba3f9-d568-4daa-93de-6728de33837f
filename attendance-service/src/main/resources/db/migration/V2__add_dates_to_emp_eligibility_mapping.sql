-- Add start and end date columns to EMP_ELIGIBILITY_MAPPING table
ALTER TABLE EMP_ELIGIBILITY_MAPPING
ADD COLUMN START_DATE DATE,
ADD COLUMN END_DATE DATE;

-- Add comments for the new columns
COMMENT ON COLUMN EMP_ELIGIBILITY_MAPPING.START_DATE IS 'Start date of the eligibility mapping (for temporary transfers)';
COMMENT ON COLUMN EMP_ELIGIBILITY_MAPPING.END_DATE IS 'End date of the eligibility mapping (for temporary transfers)';

-- Create index for date-based queries
CREATE INDEX idx_emp_eligibility_dates ON EMP_ELIGIBILITY_MAPPING(START_DATE, END_DATE); 