-- Fix EMP_SHIFT_MAPPING table structure for Reladomo compatibility
-- Change MAPPING_ID to ID to match Reladomo domain object

-- Drop existing foreign key constraint first
ALTER TABLE EMP_SHIFT_MAPPING DROP FOREIGN KEY FK_EMP_SHIFT_MAPPING_SHIFT_ID;

-- Drop existing indexes that reference the old column
DROP INDEX idx_emp_shift_mapping_shift_id ON EMP_SHIFT_MAPPING;
DROP INDEX idx_emp_shift_mapping_emp_id ON EMP_SHIFT_MAPPING;
DROP INDEX idx_emp_shift_mapping_status ON EMP_SHIFT_MAPPING;
DROP INDEX idx_emp_shift_mapping_start_date ON EMP_SHIFT_MAPPING;
DROP INDEX idx_emp_shift_mapping_end_date ON EMP_SHIFT_MAPPING;
DROP INDEX idx_emp_shift_mapping_emp_status ON EMP_SHIFT_MAPPING;
DROP INDEX idx_emp_shift_mapping_shift_status ON EMP_SHIFT_MAPPING;
DROP INDEX idx_emp_shift_mapping_date_range ON EMP_SHIFT_MAPPING;

-- Rename MAPPING_ID to ID to match Reladomo domain object
ALTER TABLE EMP_SHIFT_MAPPING CHANGE COLUMN MAPPING_ID ID INT AUTO_INCREMENT PRIMARY KEY;

-- Remove START_DATE and END_DATE columns as they're not in the domain object
ALTER TABLE EMP_SHIFT_MAPPING DROP COLUMN START_DATE;
ALTER TABLE EMP_SHIFT_MAPPING DROP COLUMN END_DATE;

-- Recreate foreign key constraint
ALTER TABLE EMP_SHIFT_MAPPING 
ADD CONSTRAINT FK_EMP_SHIFT_MAPPING_SHIFT_ID 
FOREIGN KEY (SHIFT_ID) REFERENCES SHIFTS(SHIFT_ID);

-- Recreate indexes for better performance
CREATE INDEX idx_emp_shift_mapping_shift_id ON EMP_SHIFT_MAPPING(SHIFT_ID);
CREATE INDEX idx_emp_shift_mapping_emp_id ON EMP_SHIFT_MAPPING(EMP_ID);
CREATE INDEX idx_emp_shift_mapping_status ON EMP_SHIFT_MAPPING(STATUS);
CREATE INDEX idx_emp_shift_mapping_expected_start ON EMP_SHIFT_MAPPING(EXPECTED_START_DATE);
CREATE INDEX idx_emp_shift_mapping_expected_end ON EMP_SHIFT_MAPPING(EXPECTED_END_DATE);

-- Create composite indexes for common queries
CREATE INDEX idx_emp_shift_mapping_emp_status ON EMP_SHIFT_MAPPING(EMP_ID, STATUS);
CREATE INDEX idx_emp_shift_mapping_shift_status ON EMP_SHIFT_MAPPING(SHIFT_ID, STATUS);

-- Create bitemporal indexes for Reladomo performance
CREATE INDEX idx_emp_shift_mapping_business_dates ON EMP_SHIFT_MAPPING(BUSINESS_FROM, BUSINESS_TO);
CREATE INDEX idx_emp_shift_mapping_processing_dates ON EMP_SHIFT_MAPPING(PROCESSING_FROM, PROCESSING_TO);
CREATE INDEX idx_emp_shift_mapping_bitemporal ON EMP_SHIFT_MAPPING(EMP_ID, BUSINESS_FROM, BUSINESS_TO, PROCESSING_FROM, PROCESSING_TO);

-- Update comments to reflect the correct column name
COMMENT ON COLUMN EMP_SHIFT_MAPPING.ID IS 'Primary key for employee shift mapping (renamed from MAPPING_ID for Reladomo compatibility)';
