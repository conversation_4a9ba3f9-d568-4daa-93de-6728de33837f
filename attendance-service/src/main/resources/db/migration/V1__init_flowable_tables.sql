---- Create Flowable tables if they don't exist
--CREATE TABLE IF NOT EXISTS ACT_GE_PROPERTY (
--    NAME_ VARCHAR(64) NOT NULL,
--    VALUE_ VARCHAR(300),
--    REV_ INT,
--    PRIMARY KEY (NAME_)
--);
--
---- Insert all necessary version properties
--INSERT INTO ACT_GE_PROPERTY (NAME_, VALUE_, REV_) VALUES
--('common.schema.version', '*******', 1),
--('next.dbid', '1', 1),
--('schema.history', 'create(*******)', 1),
--('schema.version', '*******', 1),
--('events.schema.version', '*******', 1),
--('eventregistry.schema.version', '*******', 1),
--('process.schema.version', '*******', 1)
--ON DUPLICATE KEY UPDATE VALUE_ = VALUES(VALUE_), REV_ = VALUES(REV_);
--
---- Event Registry Tables
--CREATE TABLE IF NOT EXISTS ACT_ER_EVENT_DEFINITION (
--    ID_ VARCHAR(64) NOT NULL,
--    NAME_ VARCHAR(255) NOT NULL,
--    DESCRIPTION_ VARCHAR(1024),
--    KEY_ VARCHAR(255) NOT NULL,
--    VERSION_ INT NOT NULL,
--    RESOURCE_NAME_ VARCHAR(255),
--    DEPLOYMENT_ID_ VARCHAR(64),
--    TENANT_ID_ VARCHAR(255) DEFAULT '',
--    PRIMARY KEY (ID_)
--);
--
--CREATE TABLE IF NOT EXISTS ACT_ER_EVENT_SUBSCRIPTION (
--    ID_ VARCHAR(64) NOT NULL,
--    EVENT_TYPE_ VARCHAR(255) NOT NULL,
--    EVENT_NAME_ VARCHAR(255) NOT NULL,
--    EXECUTION_ID_ VARCHAR(64) NOT NULL,
--    PROCESS_INSTANCE_ID_ VARCHAR(64) NOT NULL,
--    ACTIVITY_ID_ VARCHAR(64) NOT NULL,
--    CONFIGURATION_ VARCHAR(255),
--    CREATED_ DATETIME(3) NOT NULL,
--    TENANT_ID_ VARCHAR(255) DEFAULT '',
--    PRIMARY KEY (ID_)
--);
--
--CREATE TABLE IF NOT EXISTS ACT_ER_CHANNEL_DEFINITION (
--    ID_ VARCHAR(64) NOT NULL,
--    NAME_ VARCHAR(255) NOT NULL,
--    DESCRIPTION_ VARCHAR(1024),
--    KEY_ VARCHAR(255) NOT NULL,
--    VERSION_ INT NOT NULL,
--    RESOURCE_NAME_ VARCHAR(255),
--    DEPLOYMENT_ID_ VARCHAR(64),
--    TENANT_ID_ VARCHAR(255) DEFAULT '',
--    PRIMARY KEY (ID_)
--);
--
---- Process Engine Tables
--CREATE TABLE IF NOT EXISTS ACT_RE_DEPLOYMENT (
--    ID_ VARCHAR(64) NOT NULL,
--    NAME_ VARCHAR(255),
--    CATEGORY_ VARCHAR(255),
--    KEY_ VARCHAR(255),
--    TENANT_ID_ VARCHAR(255) DEFAULT '',
--    DEPLOY_TIME_ DATETIME(3),
--    ENGINE_VERSION_ VARCHAR(255),
--    PRIMARY KEY (ID_)
--);
--
--CREATE TABLE IF NOT EXISTS ACT_RE_MODEL (
--    ID_ VARCHAR(64) NOT NULL,
--    REV_ INT,
--    NAME_ VARCHAR(255),
--    KEY_ VARCHAR(255),
--    CATEGORY_ VARCHAR(255),
--    CREATE_TIME_ DATETIME(3),
--    LAST_UPDATE_TIME_ DATETIME(3),
--    VERSION_ INT,
--    META_INFO_ VARCHAR(4000),
--    DEPLOYMENT_ID_ VARCHAR(64),
--    EDITOR_SOURCE_VALUE_ID_ VARCHAR(64),
--    EDITOR_SOURCE_EXTRA_VALUE_ID_ VARCHAR(64),
--    TENANT_ID_ VARCHAR(255) DEFAULT '',
--    PRIMARY KEY (ID_)
--);
--
--CREATE TABLE IF NOT EXISTS ACT_RE_PROCDEF (
--    ID_ VARCHAR(64) NOT NULL,
--    REV_ INT,
--    CATEGORY_ VARCHAR(255),
--    NAME_ VARCHAR(255),
--    KEY_ VARCHAR(255) NOT NULL,
--    VERSION_ INT NOT NULL,
--    DEPLOYMENT_ID_ VARCHAR(64),
--    RESOURCE_NAME_ VARCHAR(4000),
--    DGRM_RESOURCE_NAME_ VARCHAR(4000),
--    DESCRIPTION_ VARCHAR(4000),
--    HAS_START_FORM_KEY_ TINYINT,
--    HAS_GRAPHICAL_NOTATION_ TINYINT,
--    SUSPENSION_STATE_ INT,
--    TENANT_ID_ VARCHAR(255) DEFAULT '',
--    ENGINE_VERSION_ VARCHAR(255),
--    PRIMARY KEY (ID_)
--);
--
--CREATE TABLE IF NOT EXISTS ACT_RU_EXECUTION (
--    ID_ VARCHAR(64) NOT NULL,
--    REV_ INT,
--    PROC_INST_ID_ VARCHAR(64),
--    BUSINESS_KEY_ VARCHAR(255),
--    PARENT_ID_ VARCHAR(64),
--    PROC_DEF_ID_ VARCHAR(64),
--    SUPER_EXEC_ VARCHAR(64),
--    ROOT_PROC_INST_ID_ VARCHAR(64),
--    ACT_ID_ VARCHAR(255),
--    IS_ACTIVE_ TINYINT,
--    IS_CONCURRENT_ TINYINT,
--    IS_SCOPE_ TINYINT,
--    IS_EVENT_SCOPE_ TINYINT,
--    IS_MI_ROOT_ TINYINT,
--    SUSPENSION_STATE_ INT,
--    CACHED_ENT_STATE_ INT,
--    TENANT_ID_ VARCHAR(255) DEFAULT '',
--    NAME_ VARCHAR(255),
--    START_TIME_ DATETIME(3),
--    START_USER_ID_ VARCHAR(255),
--    LOCK_TIME_ DATETIME(3),
--    IS_COUNT_ENABLED_ TINYINT,
--    EVT_SUBSCR_COUNT_ INT,
--    TASK_COUNT_ INT,
--    JOB_COUNT_ INT,
--    TIMER_JOB_COUNT_ INT,
--    SUSP_JOB_COUNT_ INT,
--    DEADLETTER_JOB_COUNT_ INT,
--    VAR_COUNT_ INT,
--    ID_LINK_COUNT_ INT,
--    PRIMARY KEY (ID_)
--);
--
--CREATE TABLE IF NOT EXISTS ACT_RU_TASK (
--    ID_ VARCHAR(64) NOT NULL,
--    REV_ INT,
--    EXECUTION_ID_ VARCHAR(64),
--    PROC_INST_ID_ VARCHAR(64),
--    PROC_DEF_ID_ VARCHAR(64),
--    NAME_ VARCHAR(255),
--    PARENT_TASK_ID_ VARCHAR(64),
--    DESCRIPTION_ VARCHAR(4000),
--    TASK_DEF_KEY_ VARCHAR(255),
--    OWNER_ VARCHAR(255),
--    ASSIGNEE_ VARCHAR(255),
--    DELEGATION_ VARCHAR(64),
--    PRIORITY_ INT,
--    CREATE_TIME_ DATETIME(3),
--    DUE_DATE_ DATETIME(3),
--    CATEGORY_ VARCHAR(255),
--    SUSPENSION_STATE_ INT,
--    TENANT_ID_ VARCHAR(255) DEFAULT '',
--    FORM_KEY_ VARCHAR(255),
--    CLAIM_TIME_ DATETIME(3),
--    IS_COUNT_ENABLED_ TINYINT,
--    VAR_COUNT_ INT,
--    ID_LINK_COUNT_ INT,
--    SUB_TASK_COUNT_ INT,
--    PRIMARY KEY (ID_)
--);
--
--CREATE TABLE IF NOT EXISTS ACT_RU_VARIABLE (
--    ID_ VARCHAR(64) NOT NULL,
--    REV_ INT,
--    TYPE_ VARCHAR(255) NOT NULL,
--    NAME_ VARCHAR(255) NOT NULL,
--    EXECUTION_ID_ VARCHAR(64),
--    PROC_INST_ID_ VARCHAR(64),
--    TASK_ID_ VARCHAR(64),
--    BYTEARRAY_ID_ VARCHAR(64),
--    DOUBLE_ DOUBLE,
--    LONG_ BIGINT,
--    TEXT_ VARCHAR(4000),
--    TEXT2_ VARCHAR(4000),
--    PRIMARY KEY (ID_)
--);
--
--CREATE TABLE IF NOT EXISTS ACT_RU_IDENTITYLINK (
--    ID_ VARCHAR(64) NOT NULL,
--    REV_ INT,
--    GROUP_ID_ VARCHAR(255),
--    TYPE_ VARCHAR(255),
--    USER_ID_ VARCHAR(255),
--    TASK_ID_ VARCHAR(64),
--    PROC_INST_ID_ VARCHAR(64),
--    PROC_DEF_ID_ VARCHAR(64),
--    PRIMARY KEY (ID_)
--);
--
--CREATE TABLE IF NOT EXISTS ACT_RU_EVENT_SUBSCR (
--    ID_ VARCHAR(64) NOT NULL,
--    REV_ INT,
--    EVENT_TYPE_ VARCHAR(255) NOT NULL,
--    EVENT_NAME_ VARCHAR(255),
--    EXECUTION_ID_ VARCHAR(64),
--    PROC_INST_ID_ VARCHAR(64),
--    ACTIVITY_ID_ VARCHAR(64),
--    CONFIGURATION_ VARCHAR(255),
--    CREATED_ DATETIME(3) NOT NULL,
--    PROC_DEF_ID_ VARCHAR(64),
--    TENANT_ID_ VARCHAR(255) DEFAULT '',
--    PRIMARY KEY (ID_)
--);
--
--CREATE TABLE IF NOT EXISTS ACT_RU_JOB (
--    ID_ VARCHAR(64) NOT NULL,
--    REV_ INT,
--    TYPE_ VARCHAR(255) NOT NULL,
--    LOCK_EXP_TIME_ DATETIME(3),
--    LOCK_OWNER_ VARCHAR(255),
--    EXCLUSIVE_ TINYINT,
--    EXECUTION_ID_ VARCHAR(64),
--    PROCESS_INSTANCE_ID_ VARCHAR(64),
--    PROC_DEF_ID_ VARCHAR(64),
--    ELEMENT_ID_ VARCHAR(255),
--    ELEMENT_NAME_ VARCHAR(255),
--    RETRIES_ INT,
--    EXCEPTION_STACK_ID_ VARCHAR(64),
--    EXCEPTION_MSG_ VARCHAR(4000),
--    DUEDATE_ DATETIME(3),
--    REPEAT_ VARCHAR(255),
--    HANDLER_TYPE_ VARCHAR(255),
--    HANDLER_CFG_ VARCHAR(4000),
--    CUSTOM_VALUES_ID_ VARCHAR(64),
--    CREATE_TIME_ DATETIME(3),
--    TENANT_ID_ VARCHAR(255) DEFAULT '',
--    PRIMARY KEY (ID_)
--);
--
--CREATE TABLE IF NOT EXISTS ACT_RU_TIMER_JOB (
--    ID_ VARCHAR(64) NOT NULL,
--    REV_ INT,
--    TYPE_ VARCHAR(255) NOT NULL,
--    LOCK_EXP_TIME_ DATETIME(3),
--    LOCK_OWNER_ VARCHAR(255),
--    EXCLUSIVE_ TINYINT,
--    EXECUTION_ID_ VARCHAR(64),
--    PROCESS_INSTANCE_ID_ VARCHAR(64),
--    PROC_DEF_ID_ VARCHAR(64),
--    ELEMENT_ID_ VARCHAR(255),
--    ELEMENT_NAME_ VARCHAR(255),
--    RETRIES_ INT,
--    EXCEPTION_STACK_ID_ VARCHAR(64),
--    EXCEPTION_MSG_ VARCHAR(4000),
--    DUEDATE_ DATETIME(3),
--    REPEAT_ VARCHAR(255),
--    HANDLER_TYPE_ VARCHAR(255),
--    HANDLER_CFG_ VARCHAR(4000),
--    CUSTOM_VALUES_ID_ VARCHAR(64),
--    CREATE_TIME_ DATETIME(3),
--    TENANT_ID_ VARCHAR(255) DEFAULT '',
--    PRIMARY KEY (ID_)
--);
--
--CREATE TABLE IF NOT EXISTS ACT_RU_SUSPENDED_JOB (
--    ID_ VARCHAR(64) NOT NULL,
--    REV_ INT,
--    TYPE_ VARCHAR(255) NOT NULL,
--    EXCLUSIVE_ TINYINT,
--    EXECUTION_ID_ VARCHAR(64),
--    PROCESS_INSTANCE_ID_ VARCHAR(64),
--    PROC_DEF_ID_ VARCHAR(64),
--    ELEMENT_ID_ VARCHAR(255),
--    ELEMENT_NAME_ VARCHAR(255),
--    RETRIES_ INT,
--    EXCEPTION_STACK_ID_ VARCHAR(64),
--    EXCEPTION_MSG_ VARCHAR(4000),
--    DUEDATE_ DATETIME(3),
--    REPEAT_ VARCHAR(255),
--    HANDLER_TYPE_ VARCHAR(255),
--    HANDLER_CFG_ VARCHAR(4000),
--    CUSTOM_VALUES_ID_ VARCHAR(64),
--    CREATE_TIME_ DATETIME(3),
--    TENANT_ID_ VARCHAR(255) DEFAULT '',
--    PRIMARY KEY (ID_)
--);
--
--CREATE TABLE IF NOT EXISTS ACT_RU_DEADLETTER_JOB (
--    ID_ VARCHAR(64) NOT NULL,
--    REV_ INT,
--    TYPE_ VARCHAR(255) NOT NULL,
--    EXCLUSIVE_ TINYINT,
--    EXECUTION_ID_ VARCHAR(64),
--    PROCESS_INSTANCE_ID_ VARCHAR(64),
--    PROC_DEF_ID_ VARCHAR(64),
--    ELEMENT_ID_ VARCHAR(255),
--    ELEMENT_NAME_ VARCHAR(255),
--    EXCEPTION_STACK_ID_ VARCHAR(64),
--    EXCEPTION_MSG_ VARCHAR(4000),
--    DUEDATE_ DATETIME(3),
--    REPEAT_ VARCHAR(255),
--    HANDLER_TYPE_ VARCHAR(255),
--    HANDLER_CFG_ VARCHAR(4000),
--    CUSTOM_VALUES_ID_ VARCHAR(64),
--    CREATE_TIME_ DATETIME(3),
--    TENANT_ID_ VARCHAR(255) DEFAULT '',
--    PRIMARY KEY (ID_)
--);
--
--CREATE TABLE IF NOT EXISTS ACT_RU_HISTORY_JOB (
--    ID_ VARCHAR(64) NOT NULL,
--    REV_ INT,
--    LOCK_EXP_TIME_ DATETIME(3),
--    LOCK_OWNER_ VARCHAR(255),
--    RETRIES_ INT,
--    EXCEPTION_STACK_ID_ VARCHAR(64),
--    EXCEPTION_MSG_ VARCHAR(4000),
--    HANDLER_TYPE_ VARCHAR(255),
--    HANDLER_CFG_ VARCHAR(4000),
--    CUSTOM_VALUES_ID_ VARCHAR(64),
--    ADV_HANDLER_CFG_ID_ VARCHAR(64),
--    CREATE_TIME_ DATETIME(3) NOT NULL,
--    TENANT_ID_ VARCHAR(255) DEFAULT '',
--    PRIMARY KEY (ID_)
--);
--
--CREATE TABLE IF NOT EXISTS ACT_RU_BYTEARRAY (
--    ID_ VARCHAR(64) NOT NULL,
--    REV_ INT,
--    NAME_ VARCHAR(255),
--    DEPLOYMENT_ID_ VARCHAR(64),
--    BYTES_ LONGBLOB,
--    GENERATED_ TINYINT,
--    TENANT_ID_ VARCHAR(255) DEFAULT '',
--    PRIMARY KEY (ID_)
--);