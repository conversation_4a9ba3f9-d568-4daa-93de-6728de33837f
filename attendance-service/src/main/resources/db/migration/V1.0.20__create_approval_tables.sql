-- Create approval tables
CREATE TABLE APPROVAL_REQUEST (
    REQUEST_ID BIGINT PRIMARY KEY AUTO_INCREMENT,
    REQUEST_TYPE VARCHAR(50) NOT NULL,
    REQUESTER_ID BIGINT NOT NULL,
    REQUESTER_NAME VARCHAR(100),
    REQUEST_DATE TIMESTAMP NOT NULL,
    DESC<PERSON>PTION TEXT,
    METADATA JSONB,
    PROCESS_INSTANCE_ID VARCHAR(36),
    STATUS VARCHAR(20) NOT NULL,
    CURRENT_STEP INT NOT NULL DEFAULT 1,
    CREATED_DATE TIMESTAMP NOT NULL,
    CREATED_BY VARCHAR(36) NOT NULL,
    UPDATED_DATE TIMESTAMP NOT NULL,
    UPDATED_BY VARCHAR(36) NOT NULL,
    DELETED_DATE TIMESTAMP,
    DELETED_BY VARCHAR(36),
    FOR<PERSON><PERSON><PERSON> KEY (REQUESTER_ID) REFERENCES USER(EMP_ID)
);

CREATE TABLE APPROVAL_STEP (
    STEP_ID BIGINT PRIMARY KEY AUTO_INCREMENT,
    REQUEST_ID BIGINT NOT NULL,
    STEP_NUMBER INT NOT NULL,
    STEP_TYPE VARCHAR(20) NOT NULL,
    APPROVER_ID BIGINT NOT NULL,
    APPROVER_NAME VARCHAR(100),
    STATUS VARCHAR(20) NOT NULL,
    ACTION_DATE TIMESTAMP,
    REMARKS TEXT,
    TASK_ID VARCHAR(36),
    REQUIRED_APPROVALS INT,
    CURRENT_APPROVALS INT,
    CREATED_DATE TIMESTAMP NOT NULL,
    CREATED_BY VARCHAR(36) NOT NULL,
    UPDATED_DATE TIMESTAMP NOT NULL,
    UPDATED_BY VARCHAR(36) NOT NULL,
    DELETED_DATE TIMESTAMP,
    DELETED_BY VARCHAR(36),
    FOREIGN KEY (REQUEST_ID) REFERENCES APPROVAL_REQUEST(REQUEST_ID)
);

-- Create indexes
CREATE INDEX IDX_APR_REQ_TYPE ON APPROVAL_REQUEST(REQUEST_TYPE);
CREATE INDEX IDX_APR_STATUS ON APPROVAL_REQUEST(STATUS);
CREATE INDEX IDX_APR_REQUESTER ON APPROVAL_REQUEST(REQUESTER_ID);
CREATE INDEX IDX_APR_STEP_REQUEST ON APPROVAL_STEP(REQUEST_ID);
CREATE INDEX IDX_APR_STEP_APPROVER ON APPROVAL_STEP(APPROVER_ID);
CREATE INDEX IDX_APR_STEP_STATUS ON APPROVAL_STEP(STATUS); 