-- Create notification tables
CREATE TABLE NOTIFICATION (
    NOTIFICATION_ID VARCHAR(36) PRIMARY KEY,
    TYPE VARCHAR(50) NOT NULL,
    TITLE VARCHAR(100) NOT NULL,
    MESSAGE TEXT NOT NULL,
    PRIORITY VARCHAR(20),
    METADATA JSON,
    CREATED_DATE TIMESTAMP NOT NULL,
    CREATED_BY VARCHAR(36) NOT NULL,
    UPDATED_DATE TIMESTAMP NOT NULL,
    UPDATED_BY VARCHAR(36) NOT NULL,
    DELETED_DATE TIMESTAMP,
    DELETED_BY VARCHAR(36)
);

CREATE TABLE NOTIFICATION_RECIPIENT (
    RECIPIENT_ID VARCHAR(36) PRIMARY KEY,
    NOTIFICATION_ID VARCHAR(36) NOT NULL,
    USER_ID VARCHAR(36) NOT NULL,
    STATUS VARCHAR(20),
    READ_<PERSON>AT<PERSON> TIMESTAMP,
    CREATED_DATE TIMESTAMP NOT NULL,
    CREATED_BY VARCHAR(36) NOT NULL,
    UPDATED_DATE TIMESTAMP NOT NULL,
    UPDATED_BY VARCHAR(36) NOT NULL,
    DELETED_DATE TIMESTAMP,
    DELETED_BY VARCHAR(36),
    FOREIGN KEY (NOTIFICATION_ID) REFERENCES NOTIFICATION(NOTIFICATION_ID),
    FOREIGN KEY (USER_ID) REFERENCES USER(EMP_ID)
);

-- Create indexes
CREATE INDEX IDX_NOTIF_TYPE ON NOTIFICATION(TYPE);
CREATE INDEX IDX_NOTIF_CREATED ON NOTIFICATION(CREATED_DATE);
CREATE INDEX IDX_NOTIF_RECIPIENT_USER ON NOTIFICATION_RECIPIENT(USER_ID);
CREATE INDEX IDX_NOTIF_RECIPIENT_STATUS ON NOTIFICATION_RECIPIENT(STATUS);
CREATE INDEX IDX_NOTIF_RECIPIENT_READ ON NOTIFICATION_RECIPIENT(READ_DATE); 