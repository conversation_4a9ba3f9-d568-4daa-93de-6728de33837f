-- Create approval step configuration table
CREATE TABLE APPROVAL_STEP_CONFIG (
    CONFIG_ID BIGINT PRIMARY KEY AUTO_INCREMENT,
    REQUEST_TYPE VARCHAR(50) NOT NULL,
    STEP_NAME VARCHAR(100) NOT NULL,
    STEP_TYPE VARCHAR(20) NOT NULL,
    REQUIRED_APPROVALS INT NOT NULL,
    APPROVER_IDS TEXT NOT NULL,
    IS_ACTIVE BOOLEAN NOT NULL DEFAULT TRUE,
    STEP_ORDER INT NOT NULL,
    ALLOW_PARTIAL_APPROVAL BOOLEAN,
    MINIMUM_APPROVALS INT,
    REQUIRE_ALL_APPROVALS BOOLEAN,
    CONDITION TEXT,
    TIMEOUT_HOURS INT,
    TIMEOUT_ACTION VARCHAR(50),
    CREATED_DATE TIMESTAMP NOT NULL,
    CREATED_BY VARCHAR(36) NOT NULL,
    UPDATED_DATE TIMESTAMP NOT NULL,
    UPDATED_BY VARCHAR(36) NOT NULL,
    DELETED_DATE TIMESTAMP,
    DELETED_BY VARCHAR(36)
);

-- Create indexes
CREATE INDEX IDX_APR_STEP_CONFIG_TYPE ON APPROVAL_STEP_CONFIG(REQUEST_TYPE);
CREATE INDEX IDX_APR_STEP_CONFIG_ACTIVE ON APPROVAL_STEP_CONFIG(IS_ACTIVE);
CREATE INDEX IDX_APR_STEP_CONFIG_ORDER ON APPROVAL_STEP_CONFIG(STEP_ORDER);

-- Add unique constraint for active steps
CREATE UNIQUE INDEX UQ_APR_STEP_CONFIG_ACTIVE 
ON APPROVAL_STEP_CONFIG(REQUEST_TYPE, STEP_NAME) 
WHERE IS_ACTIVE = TRUE; 