-- Create new tables with auto-increment
CREATE TABLE APPROVAL_REQUEST_NEW (
    ID BIGINT AUTO_INCREMENT PRIMARY KEY,
    REQUESTER_ID VARCHAR(36) NOT NULL,
    REQUEST_TYPE VARCHAR(50) NOT NULL,
    TOTAL_STEPS INT NOT NULL,
    CURRENT_STEP INT NOT NULL,
    STATUS VARCHAR(20) NOT NULL,
    CREATED_DATE TIMESTAMP NOT NULL,
    CREATED_BY VARCHAR(36) NOT NULL,
    UPDATED_DATE TIMESTAMP NOT NULL,
    UPDATED_BY VARCHAR(36) NOT NULL,
    DELETED_DATE TIMESTAMP,
    DELETED_BY VARCHAR(36),
    FOREIGN KEY (REQUESTER_ID) REFERENCES USER(EMP_ID)
);

CREATE TABLE APPROVAL_STEP_NEW (
    ID BIGINT AUTO_INCREMENT PRIMARY KEY,
    REQUEST_ID BIGINT NOT NULL,
    STEP_NUMBER INT NOT NULL,
    APPROVER_ID VARCHAR(36),
    <PERSON>ATUS VARCHAR(20) NOT NULL,
    COMMENTS TEXT,
    CREATED_DATE TIMESTAMP NOT NULL,
    CREATED_BY VARCHAR(36) NOT NULL,
    UPDATED_DATE TIMESTAMP NOT NULL,
    UPDATED_BY VARCHAR(36) NOT NULL,
    DELETED_DATE TIMESTAMP,
    DELETED_BY VARCHAR(36),
    FOREIGN KEY (REQUEST_ID) REFERENCES APPROVAL_REQUEST_NEW(ID)
);

-- Migrate data
INSERT INTO APPROVAL_REQUEST_NEW (
    REQUESTER_ID, REQUEST_TYPE, TOTAL_STEPS, CURRENT_STEP,
    STATUS, CREATED_DATE, CREATED_BY, UPDATED_DATE, UPDATED_BY,
    DELETED_DATE, DELETED_BY
)
SELECT 
    REQUESTER_ID, REQUEST_TYPE, TOTAL_STEPS, CURRENT_STEP,
    STATUS, CREATED_DATE, CREATED_BY, UPDATED_DATE, UPDATED_BY,
    DELETED_DATE, DELETED_BY
FROM APPROVAL_REQUEST;

INSERT INTO APPROVAL_STEP_NEW (
    REQUEST_ID, STEP_NUMBER, APPROVER_ID, STATUS,
    COMMENTS, CREATED_DATE, CREATED_BY, UPDATED_DATE, UPDATED_BY,
    DELETED_DATE, DELETED_BY
)
SELECT 
    ar_new.ID, as_old.STEP_NUMBER, as_old.APPROVER_ID, as_old.STATUS,
    as_old.COMMENTS, as_old.CREATED_DATE, as_old.CREATED_BY, as_old.UPDATED_DATE, as_old.UPDATED_BY,
    as_old.DELETED_DATE, as_old.DELETED_BY
FROM APPROVAL_STEP as_old
JOIN APPROVAL_REQUEST ar_old ON as_old.REQUEST_ID = ar_old.REQUEST_ID
JOIN APPROVAL_REQUEST_NEW ar_new ON ar_old.REQUESTER_ID = ar_new.REQUESTER_ID
    AND ar_old.CREATED_DATE = ar_new.CREATED_DATE;

-- Create indexes
CREATE INDEX IDX_AR_REQ_TYPE ON APPROVAL_REQUEST_NEW(REQUEST_TYPE);
CREATE INDEX IDX_AR_STATUS ON APPROVAL_REQUEST_NEW(STATUS);
CREATE INDEX IDX_AR_REQUESTER ON APPROVAL_REQUEST_NEW(REQUESTER_ID);
CREATE INDEX IDX_AS_REQUEST ON APPROVAL_STEP_NEW(REQUEST_ID);
CREATE INDEX IDX_AS_APPROVER ON APPROVAL_STEP_NEW(APPROVER_ID);
CREATE INDEX IDX_AS_STATUS ON APPROVAL_STEP_NEW(STATUS);

-- Rename tables
RENAME TABLE APPROVAL_REQUEST TO APPROVAL_REQUEST_OLD;
RENAME TABLE APPROVAL_STEP TO APPROVAL_STEP_OLD;
RENAME TABLE APPROVAL_REQUEST_NEW TO APPROVAL_REQUEST;
RENAME TABLE APPROVAL_STEP_NEW TO APPROVAL_STEP;

-- Drop old tables after verification
-- DROP TABLE APPROVAL_REQUEST_OLD;
-- DROP TABLE APPROVAL_STEP_OLD; 