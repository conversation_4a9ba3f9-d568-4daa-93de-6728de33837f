<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
             xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
             xmlns:flowable="http://flowable.org/bpmn"
             targetNamespace="http://www.flowable.org/processdef">

    <process id="approvalProcess" name="Approval Process" isExecutable="true">
        <startEvent id="startEvent" name="Start Approval Process"/>
        
        <sequenceFlow id="flow1" sourceRef="startEvent" targetRef="step1Gateway"/>
        
        <!-- Step 1: Parallel approvals with minimum required -->
        <parallelGateway id="step1Gateway" name="Step 1 Gateway"/>
        
        <sequenceFlow id="flow2" sourceRef="step1Gateway" targetRef="step1Task1"/>
        <sequenceFlow id="flow3" sourceRef="step1Gateway" targetRef="step1Task2"/>
        <sequenceFlow id="flow4" sourceRef="step1Gateway" targetRef="step1Task3"/>
        
        <userTask id="step1Task1" name="Step 1 Approval 1" flowable:assignee="${approver1Id}">
            <documentation>First approver for step 1</documentation>
            <extensionElements>
                <flowable:formProperty id="decision" name="Decision" type="enum" required="true">
                    <flowable:value id="APPROVE" name="Approve"/>
                    <flowable:value id="REJECT" name="Reject"/>
                </flowable:formProperty>
                <flowable:formProperty id="remarks" name="Remarks" type="string"/>
            </extensionElements>
        </userTask>
        
        <userTask id="step1Task2" name="Step 1 Approval 2" flowable:assignee="${approver2Id}">
            <documentation>Second approver for step 1</documentation>
            <extensionElements>
                <flowable:formProperty id="decision" name="Decision" type="enum" required="true">
                    <flowable:value id="APPROVE" name="Approve"/>
                    <flowable:value id="REJECT" name="Reject"/>
                </flowable:formProperty>
                <flowable:formProperty id="remarks" name="Remarks" type="string"/>
            </extensionElements>
        </userTask>
        
        <userTask id="step1Task3" name="Step 1 Approval 3" flowable:assignee="${approver3Id}">
            <documentation>Third approver for step 1</documentation>
            <extensionElements>
                <flowable:formProperty id="decision" name="Decision" type="enum" required="true">
                    <flowable:value id="APPROVE" name="Approve"/>
                    <flowable:value id="REJECT" name="Reject"/>
                </flowable:formProperty>
                <flowable:formProperty id="remarks" name="Remarks" type="string"/>
            </extensionElements>
        </userTask>
        
        <parallelGateway id="step1Join" name="Step 1 Join">
            <extensionElements>
                <flowable:executionListener event="start" class="com.stpl.tech.attendance.workflow.listener.Step1CompletionListener"/>
            </extensionElements>
        </parallelGateway>
        
        <sequenceFlow id="flow5" sourceRef="step1Task1" targetRef="step1Join"/>
        <sequenceFlow id="flow6" sourceRef="step1Task2" targetRef="step1Join"/>
        <sequenceFlow id="flow7" sourceRef="step1Task3" targetRef="step1Join"/>
        
        <sequenceFlow id="flow8" sourceRef="step1Join" targetRef="step2Task"/>
        
        <!-- Step 2: Single approver -->
        <userTask id="step2Task" name="Step 2 Approval" flowable:assignee="${step2ApproverId}">
            <documentation>Final approval step</documentation>
            <extensionElements>
                <flowable:formProperty id="decision" name="Decision" type="enum" required="true">
                    <flowable:value id="APPROVE" name="Approve"/>
                    <flowable:value id="REJECT" name="Reject"/>
                </flowable:formProperty>
                <flowable:formProperty id="remarks" name="Remarks" type="string"/>
            </extensionElements>
        </userTask>
        
        <sequenceFlow id="flow9" sourceRef="step2Task" targetRef="decisionGateway"/>
        
        <exclusiveGateway id="decisionGateway" name="Decision Gateway"/>
        
        <sequenceFlow id="flow10" sourceRef="decisionGateway" targetRef="approvedEnd">
            <conditionExpression xsi:type="tFormalExpression">${decision == 'APPROVE'}</conditionExpression>
        </sequenceFlow>
        
        <sequenceFlow id="flow11" sourceRef="decisionGateway" targetRef="rejectedEnd">
            <conditionExpression xsi:type="tFormalExpression">${decision == 'REJECT'}</conditionExpression>
        </sequenceFlow>
        
        <endEvent id="approvedEnd" name="Approved">
            <extensionElements>
                <flowable:executionListener event="end" class="com.stpl.tech.attendance.workflow.listener.ApprovalProcessEndListener"/>
            </extensionElements>
        </endEvent>
        
        <endEvent id="rejectedEnd" name="Rejected">
            <extensionElements>
                <flowable:executionListener event="end" class="com.stpl.tech.attendance.workflow.listener.ApprovalProcessEndListener"/>
            </extensionElements>
        </endEvent>
    </process>
</definitions> 