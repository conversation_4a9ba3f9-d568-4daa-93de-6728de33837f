<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
             xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
             xmlns:flowable="http://flowable.org/bpmn"
             xmlns:modeler="http://flowable.org/modeler"
             targetNamespace="http://www.flowable.org/processdef">

    <process id="attendance-approval" name="Attendance Approval Process">

        <!-- Start Event -->
        <startEvent id="startEvent" name="Start Approval Process"/>

        <!-- First Level Approval -->
        <userTask id="firstApproval" name="First Level Approval" flowable:assignee=""
                  flowable:candidateGroup="CAFE_MANAGER" >
            <documentation>First level approval by cafe manager</documentation>
            <extensionElements>
                <flowable:taskListener event="create" delegateExpression="${approvalTaskListener}" />
                <flowable:taskListener event="complete" delegateExpression="${approvalProcessEndListener}"/>
<!--                <flowable:taskListener event="assignment" class="com.stpl.tech.attendance.workflow.listener.ApprovalTaskAssignmentListener"/>-->
                <flowable:property name="approverRole" value="MANAGER"/>
                <flowable:property name="approverRole" value="manager" />
                <flowable:property name="requiredApprovals" value="2" />
                <flowable:property name="minimumApprovals" value="1" />
                <flowable:property name="requireAllApprovals" value="false" />
                <flowable:property name="allowPartialApproval" value="true" />
                <flowable:property name="currentStep" value="1" />
            </extensionElements>
        </userTask>

<!--        &lt;!&ndash; Second Level Approval &ndash;&gt;-->
<!--        <userTask id="secondApproval" name="Second Level Approval"-->
<!--                  flowable:candidateGroup="AREA_MANAGER">-->
<!--            <documentation>Second level approval by area manager</documentation>-->
<!--            <extensionElements>-->
<!--                <flowable:taskListener event="complete" delegateExpression="${approvalProcessEndListener}"/>-->
<!--                <flowable:taskListener event="create" delegateExpression="${approvalTaskListener}" />-->
<!--&lt;!&ndash;                <flowable:taskListener event="assignment" class="com.stpl.tech.attendance.workflow.listener.ApprovalTaskAssignmentListener"/>&ndash;&gt;-->
<!--                <flowable:property name="approverRole" value="manager" />-->
<!--                <flowable:property name="requiredApprovals" value="2" />-->
<!--                <flowable:property name="minimumApprovals" value="1" />-->
<!--                <flowable:property name="requireAllApprovals" value="false" />-->
<!--                <flowable:property name="allowPartialApproval" value="true" />-->
<!--                <flowable:property name="currentStep" value="2" />-->
<!--            </extensionElements>-->
<!--        </userTask>-->

<!--        &lt;!&ndash; Final Approval &ndash;&gt;-->
<!--        <userTask id="finalApproval" name="Final Approval"-->
<!--                  flowable:candidateGroup="HR"-->
<!--                  flowable:collection="approvers"-->
<!--                  flowable:elementVariable="assignee"-->
<!--                  flowable:completionCondition="${approvedCount >= 2}"-->
<!--                  flowable:multiInstance="parallel">-->
<!--            <documentation>Final approval by HR personnel</documentation>-->
<!--            <extensionElements>-->
<!--               <flowable:taskListener event="complete" delegateExpression="${approvalProcessEndListener}"/>-->
<!--                <flowable:taskListener event="create" delegateExpression="${approvalTaskListener}" />-->
<!--&lt;!&ndash;                <flowable:taskListener event="assignment" class="com.stpl.tech.attendance.workflow.listener.ApprovalTaskAssignmentListener"/>&ndash;&gt;-->
<!--                <flowable:property name="requiredApprovals" value="2" />-->
<!--                <flowable:property name="minimumApprovals" value="1" />-->
<!--                <flowable:property name="requireAllApprovals" value="false" />-->
<!--                <flowable:property name="allowPartialApproval" value="true" />-->
<!--                <flowable:property name="currentStep" value="3" />-->
<!--            </extensionElements>-->
<!--        </userTask>-->

        <!-- Decision Gateway -->
        <exclusiveGateway id="decisionGateway" name="Approval Decision"/>

        <!-- End Events -->
        <endEvent id="approved" name="Approved">
            <documentation>Request has been approved by all levels</documentation>
        </endEvent>

        <endEvent id="rejected" name="Rejected">
            <documentation>Request has been rejected</documentation>
        </endEvent>

        <!-- Sequence Flows -->
        <sequenceFlow sourceRef="startEvent" targetRef="firstApproval"/>

        <sequenceFlow sourceRef="firstApproval" targetRef="decisionGateway"/>
<!--        <sequenceFlow sourceRef="secondApproval" targetRef="decisionGateway"/>-->
<!--        <sequenceFlow sourceRef="finalApproval" targetRef="decisionGateway"/>-->

        <sequenceFlow sourceRef="decisionGateway" targetRef="approved">
            <conditionExpression xsi:type="tFormalExpression">
                ${action == 'APPROVED'}
            </conditionExpression>
        </sequenceFlow>

        <sequenceFlow sourceRef="decisionGateway" targetRef="rejected">
            <conditionExpression xsi:type="tFormalExpression">
                ${action == 'REJECTED'}
            </conditionExpression>
        </sequenceFlow>

<!--        <sequenceFlow sourceRef="decisionGateway" targetRef="secondApproval">-->
<!--            <conditionExpression xsi:type="tFormalExpression">-->
<!--                ${action == 'APPROVED' &amp;&amp; currentStep == 1}-->
<!--            </conditionExpression>-->
<!--        </sequenceFlow>-->

<!--        <sequenceFlow sourceRef="decisionGateway" targetRef="finalApproval">-->
<!--            <conditionExpression xsi:type="tFormalExpression">-->
<!--                ${action == 'APPROVED' &amp;&amp; currentStep == 2}-->
<!--            </conditionExpression>-->
<!--        </sequenceFlow>-->

    </process>
</definitions> 