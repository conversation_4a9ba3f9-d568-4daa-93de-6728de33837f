spring:
  application:
    name: attendance-service
  cloud:
    config:
      uri: ${config.url}
      fail-fast: true
      retry:
        initial-interval: 1000
        max-interval: 2000
        max-attempts: 6
      enabled: true
      request-read-timeout: 5000
      request-connect-timeout: 5000
      label: ${spring.profiles.active}
      profile: ${spring.profiles.active}
      username: ${CONFIG_SERVER_USERNAME:pakoda}
      password: ${CONFIG_SERVER_PASSWORD:CozyChaiMoment!}