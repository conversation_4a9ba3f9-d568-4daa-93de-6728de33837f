spring.application.name=attendance-service
spring.config.import=configserver:${config.url}

spring.cloud.config.label=${spring.profiles.active}
spring.cloud.config.username=pakoda
spring.cloud.config.password=CozyChaiMoment!

# Logging Configuration - Use custom logback configuration
# Remove these to let logback-spring.xml handle everything
# logging.file.path=/home/<USER>/deployment/logs/attendance-service
# logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n
# logging.pattern.console=%d{ISO8601} %-5level [%t] %C{1}: %msg%n

# Disable Spring Boot's default logging configuration








