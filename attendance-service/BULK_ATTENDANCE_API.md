# Bulk Attendance Punch API

## Overview
The Bulk Attendance Punch API allows you to process multiple attendance punch requests in a single API call. Each request in the bulk operation is processed asynchronously, providing better performance for large datasets.

## Endpoint
```
POST /api/v1/attendance/bulk-punch
```

## Request Format

### BulkAttendancePunchRequest
```json
{
  "attendancePunchRequests": [
    {
      "employeeId": 123,
      "base64Image": "base64_encoded_image_string",
      "unitId": 1,
      "punchTime": "2024-01-15T09:00:00",
      "macAddress": "AA:BB:CC:DD:EE:FF",
      "geoLocation": "latitude,longitude",
      "createdBy": "system",
      "remarks": "Bulk upload",
      "isSpecialCase": false,
      "specialCaseType": null,
      "biometricId": "bio123",
      "punchType": "CHECK_IN",
      "saveFailedImage": true
    }
  ],
  "isOfflineMode": true
}
```

### Offline Mode Usage
The `isOfflineMode` flag in the bulk request automatically sets all individual requests as offline mode:

```json
{
  "attendancePunchRequests": [
    {
      "employeeId": 123,
      "base64Image": "base64_encoded_image_string",
      "unitId": 1,
      "punchTime": "2024-01-15T09:00:00",
      "macAddress": "AA:BB:CC:DD:EE:FF",
      "geoLocation": "latitude,longitude",
      "createdBy": "system",
      "remarks": "Offline punch",
      "biometricId": "bio123",
      "punchType": "CHECK_IN",
      "saveFailedImage": true
    }
  ],
  "isOfflineMode": true
}
```

**Note**: When `isOfflineMode: true` (default), the system automatically:
- Sets `isSpecialCase: true` for all requests
- Sets `specialCaseType: "OFFLINE_MODE"` for all requests
- Adds "(Bulk Offline Sync)" to remarks if not already present

### Individual Request Offline Mode
You can also set offline mode for individual requests by setting the special case fields:
```json
{
  "employeeId": 123,
  "base64Image": "base64_encoded_image_string",
  "unitId": 1,
  "punchTime": "2024-01-15T09:00:00",
  "macAddress": "AA:BB:CC:DD:EE:FF",
  "geoLocation": "latitude,longitude",
  "createdBy": "system",
  "remarks": "Offline punch",
  "isSpecialCase": true,
  "specialCaseType": "OFFLINE_MODE",
  "biometricId": "bio123",
  "punchType": "CHECK_IN",
  "saveFailedImage": true
}
```

### Supported Special Case Types
- `TRAINING` - Training sessions
- `OFF_SITE` - Off-site work
- `WORK_FROM_HOME` - Work from home
- `BUSINESS_TRAVEL` - Business travel
- `LEAVE` - Leave
- `HOLIDAY` - Holiday
- `SYSTEM_GENERATED` - System generated entries
- `OFFLINE_MODE` - Offline mode punches

### Supported Date/Time Formats for `punchTime`
The `punchTime` field supports multiple formats:

1. **ISO Format String**: `"2024-01-15T09:00:00"`
2. **ISO Format with Milliseconds**: `"2024-01-15T09:00:00.000"`
3. **Raw Timestamp (milliseconds since epoch)**: `1750251556054`
4. **Null value**: `null` (will use current server time)

### Validation Rules
- Maximum 100 attendance punch requests per bulk operation
- Each individual request must follow the same validation rules as the single punch API
- All required fields must be present for each request
- `punchTime` is optional - if not provided, current server time will be used
- `isOfflineMode` defaults to `true` for bulk operations

## Response Format

### BulkAttendancePunchResponse
```json
{
  "success": true,
  "message": "Bulk operation completed. Success: 95, Failed: 5",
  "processedAt": "2024-01-15T10:30:00",
  "totalRequests": 100,
  "successfulRequests": 95,
  "failedRequests": 5,
  "bulkOperationId": "550e8400-e29b-41d4-a716-446655440000",
  "results": [
    {
      "success": true,
      "message": "Check-in recorded successfully",
      "punchType": "CHECK_IN",
      "punchTime": "2024-01-15T09:00:00",
      "employeeCode": "EMP001",
      "employeeName": "John Doe",
      "createdBy": "system",
      "systemGeneratedEntries": null,
      "nextAction": "SUGGESTED_MIDDAY",
      "imageUrl": "https://cloudfront.net/attendance/EMP001/1234567890.jpg",
      "registeredImageUrl": "https://cloudfront.net/employees/EMP001.jpg"
    }
  ]
}
```

## Error Handling

### Individual Request Errors
If an individual request fails, it will be included in the results with:
- `success: false`
- `message`: Description of the error
- Other fields may be null or empty

### Common Error Scenarios
1. **Invalid Employee ID**: Employee not found or inactive
2. **Invalid Unit ID**: Unit not found or employee not eligible
3. **Biometric Not Registered**: Employee's biometric not registered or not approved
4. **Duplicate Punch**: Same punch type within threshold time
5. **Invalid Punch Time**: Punch time in the future or too far in the past
6. **Image Processing Error**: Failed to process or upload image

## Usage Examples

### Regular Bulk Punch (Online Mode)
```bash
curl -X POST "http://localhost:8080/api/v1/attendance/bulk-punch" \
  -H "Content-Type: application/json" \
  -d '{
    "attendancePunchRequests": [
      {
        "employeeId": 123,
        "base64Image": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...",
        "unitId": 1,
        "punchTime": "2024-01-15T09:00:00",
        "macAddress": "AA:BB:CC:DD:EE:FF",
        "geoLocation": "12.9716,77.5946",
        "createdBy": "system",
        "remarks": "Regular punch",
        "isSpecialCase": false,
        "specialCaseType": null,
        "punchType": "CHECK_IN",
        "saveFailedImage": true
      }
    ],
    "isOfflineMode": false
  }'
```

### Offline Mode Bulk Punch (Default)
```bash
curl -X POST "http://localhost:8080/api/v1/attendance/bulk-punch" \
  -H "Content-Type: application/json" \
  -d '{
    "attendancePunchRequests": [
      {
        "employeeId": 123,
        "base64Image": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...",
        "unitId": 1,
        "punchTime": "2024-01-15T09:00:00",
        "macAddress": "AA:BB:CC:DD:EE:FF",
        "geoLocation": "12.9716,77.5946",
        "createdBy": "system",
        "remarks": "Offline punch",
        "punchType": "CHECK_IN",
        "saveFailedImage": true
      }
    ],
    "isOfflineMode": true
  }'
```

**Note**: When `isOfflineMode: true`, you don't need to set `isSpecialCase` and `specialCaseType` in individual requests - the system will automatically set them.

## Performance Considerations

1. **Async Processing**: Each request is processed asynchronously for better performance
2. **Batch Size**: Recommended batch size is 50-100 requests for optimal performance
3. **Timeout**: Large batches may take longer to process
4. **Memory Usage**: Monitor memory usage when processing large batches
5. **Database Load**: Consider database connection pool size for large operations

## Monitoring and Logging

The API provides comprehensive logging:
- Bulk operation start/end with timing and offline mode status
- Individual request processing status
- Error details for failed requests
- Performance metrics

Use the `bulkOperationId` to track specific bulk operations in logs. 