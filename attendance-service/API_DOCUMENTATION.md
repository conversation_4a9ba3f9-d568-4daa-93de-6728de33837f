# Attendance Service API Documentation

## Base URL
```
http://localhost:8081/attendance-service/api/v1
```

## Authentication
All APIs require JWT token in the Authorization header:
```
Authorization: Bearer <your_jwt_token>
```

## 1. Attendance APIs

### Mark Attendance
```http
POST /attendance/punch
```
**Request Body:**
```json
{
    "employeeId": "integer",
    "unitId": "integer",
    "punchTime": "datetime",
    "macAddress": "string",
    "geoLocation": "string",
    "createdBy": "string",
    "remarks": "string",
    "isSpecialCase": "boolean",
    "specialCaseType": "string"
}
```
**Response:**
```json
{
    "success": "boolean",
    "message": "string",
    "punchType": "CHECKIN|MIDDAY|CHECKOUT",
    "punchTime": "datetime",
    "createdBy": "string",
    "imageUrl": "string",
    "nextAction": "string"
}
```

### Get Current Day Attendance
```http
GET /attendance/today/{employeeId}
```
**Path Parameters:**
- `employeeId`: Long

**Response:**
```json
{
    "success": "boolean",
    "data": {
        "employeeId": "long",
        "employeeName": "string",
        "designation": "string",
        "checkInTime": "datetime",
        "checkOutTime": "datetime",
        "checkInImageUrl": "string",
        "checkOutImageUrl": "string",
        "status": "PRESENT|MIDDAY|ABSENT"
    }
}
```

### Get Attendance History
```http
GET /attendance/{employeeId}
```
**Path Parameters:**
- `employeeId`: Long

**Query Parameters:**
- `date`: LocalDate (optional)
- `month`: Integer (optional)
- `year`: Integer (optional)

**Response:**
```json
{
    "success": "boolean",
    "data": [
        {
            "employeeId": "long",
            "employeeName": "string",
            "designation": "string",
            "checkInTime": "datetime",
            "checkOutTime": "datetime",
            "checkInImageUrl": "string",
            "checkOutImageUrl": "string",
            "status": "PRESENT|MIDDAY|ABSENT"
        }
    ]
}
```

## 2. Approval APIs

### Get Pending Approvals
```http
GET /approvals/pending/{employeeId}
```
**Path Parameters:**
- `employeeId`: Long

**Response:**
```json
{
    "success": "boolean",
    "data": [
        {
            "requestId": "long",
            "employeeId": "string",
            "requestType": "string",
            "status": "string",
            "requestDate": "datetime",
            "lastUpdatedDate": "datetime",
            "createdBy": "string",
            "updatedBy": "string"
        }
    ]
}
```

### Get Completed Approvals
```http
GET /approvals/completed/{employeeId}
```
**Path Parameters:**
- `employeeId`: Long

**Query Parameters:**
- `pageNumber`: Integer (default: 0)
- `pageSize`: Integer (default: 10)

**Response:**
```json
{
    "success": "boolean",
    "data": [
        {
            "requestId": "long",
            "employeeId": "string",
            "requestType": "string",
            "status": "string",
            "requestDate": "datetime",
            "lastUpdatedDate": "datetime",
            "createdBy": "string",
            "updatedBy": "string"
        }
    ],
    "page": {
        "totalElements": "long",
        "totalPages": "integer",
        "size": "integer",
        "number": "integer"
    }
}
```

### Process Approval Action
```http
POST /approvals/{requestId}/action
```
**Path Parameters:**
- `requestId`: Long

**Request Body:**
```json
{
    "approverId": "string",
    "stepId": "long",
    "action": "APPROVE|REJECT",
    "remarks": "string"
}
```

**Response:**
```json
{
    "success": "boolean",
    "data": {
        "requestId": "long",
        "employeeId": "string",
        "requestType": "string",
        "status": "string",
        "requestDate": "datetime",
        "lastUpdatedDate": "datetime",
        "createdBy": "string",
        "updatedBy": "string"
    }
}
```

### Get Approval History
```http
GET /approvals/{requestId}/history
```
**Path Parameters:**
- `requestId`: Long

**Response:**
```json
{
    "success": "boolean",
    "data": [
        {
            "requestId": "long",
            "employeeId": "string",
            "requestType": "string",
            "status": "string",
            "requestDate": "datetime",
            "lastUpdatedDate": "datetime",
            "createdBy": "string",
            "updatedBy": "string"
        }
    ]
}
```

## 3. Biometric Registration APIs

### Register Biometric
```http
POST /biometric/register
```
**Request Body:**
```json
{
    "employeeId": "string",
    "unitId": "integer",
    "template": "string",
    "fingerIndex": "integer"
}
```

**Response:**
```json
{
    "success": "boolean",
    "message": "string",
    "data": {
        "registrationId": "string",
        "status": "string"
    }
}
```

## Common Response Format
All APIs follow a standard response format:
```json
{
    "success": "boolean",
    "message": "string",
    "data": "object|array",
    "page": {
        "totalElements": "long",
        "totalPages": "integer",
        "size": "integer",
        "number": "integer"
    }
}
```

## Error Responses
In case of errors, the API will return:
```json
{
    "success": false,
    "message": "Error message",
    "errorCode": "string",
    "timestamp": "datetime"
}
```

## Notes
1. All datetime fields are in ISO-8601 format
2. All APIs require JWT authentication except for Swagger UI and API documentation
3. Pagination is supported where specified
4. File uploads (if any) should use multipart/form-data 