### Controller Standards
1. All controllers MUST extend `BaseController` to maintain consistent response handling and error management
2. All controller responses MUST use the standard response format:
   ```java
   return ResponseEntity.ok(ResponseDTO.builder()
       .status(ResponseStatus.SUCCESS)
       .data(responseData)
       .message("Operation completed successfully")
       .build());
   ```
3. All controllers MUST include proper logging:
   - Log entry with request details
   - Log exit with response details
   - Log errors with stack traces
4. All controllers MUST validate input using `@Valid` annotation
5. All controllers MUST use proper HTTP status codes:
   - 200 for successful operations
   - 201 for resource creation
   - 400 for bad requests
   - 401 for unauthorized
   - 403 for forbidden
   - 404 for not found
   - 500 for server errors

### Response Handling Standards
1. All controllers MUST use `BaseController`'s response methods instead of manually creating `ResponseDTO`:
   ```java
   // DO NOT use this:
   return ResponseEntity.ok(ResponseDTO.builder()
       .status(ResponseStatus.SUCCESS)
       .data(response)
       .message("Success message")
       .build());

   // DO use this:
   return success(response, "Success message");
   ```

2. Available response methods from `BaseController`:
   - `success(T data, String message)` - For successful operations
   - `created(T data, String message)` - For resource creation (HTTP 201)
   - `error(String message, String code)` - For error responses
   - `notFound(String message)` - For resource not found (HTTP 404)
   - `badRequest(String message)` - For invalid requests (HTTP 400)
   - `unauthorized(String message)` - For unauthorized access (HTTP 401)
   - `forbidden(String message)` - For forbidden access (HTTP 403)

3. Response method usage guidelines:
   - Use `success()` for GET, PUT, PATCH operations
   - Use `created()` for POST operations that create new resources
   - Use appropriate error methods based on the error type
   - Always provide meaningful messages
   - Include relevant data in success responses

Example of a standard controller:
```java
@RestController
@RequestMapping("/api/v1/resource")
@RequiredArgsConstructor
public class ResourceController extends BaseController {
    
    private final ResourceService resourceService;
    
    @PostMapping
    public ResponseEntity<ResponseDTO<ResourceResponse>> createResource(
            @Valid @RequestBody ResourceRequest request) {
        log.info("Creating resource with request: {}", request);
        ResourceResponse response = resourceService.createResource(request);
        log.info("Resource created successfully: {}", response);
        return created(response, "Resource created successfully");
    }

    @GetMapping("/{id}")
    public ResponseEntity<ResponseDTO<ResourceResponse>> getResource(@PathVariable Long id) {
        log.info("Fetching resource with ID: {}", id);
        ResourceResponse response = resourceService.getResource(id);
        log.info("Resource fetched successfully: {}", response);
        return success(response, "Resource fetched successfully");
    }

    @PutMapping("/{id}")
    public ResponseEntity<ResponseDTO<ResourceResponse>> updateResource(
            @PathVariable Long id,
            @Valid @RequestBody ResourceRequest request) {
        log.info("Updating resource: {} with request: {}", id, request);
        ResourceResponse response = resourceService.updateResource(id, request);
        log.info("Resource updated successfully: {}", response);
        return success(response, "Resource updated successfully");
    }
} 