# Deployment Guide

This guide provides instructions for deploying the Attendance Service using Docker and Docker Compose.

## Prerequisites

- Docker (version 20.10.0 or higher)
- Docker Compose (version 2.0.0 or higher)
- Git
- At least 2GB of free disk space
- At least 1GB of RAM

## Deployment Steps

### 1. Clone the Repository

```bash
git clone <repository-url>
cd attendance-service
```

### 2. Configure Environment Variables

Create a `.env` file in the project root with the following variables (modify as needed):

```env
POSTGRES_DB=attendance_db
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_secure_password
SPRING_PROFILES_ACTIVE=prod
```

### 3. Build and Start the Services

```bash
# Build and start all services
docker-compose up -d

# To view logs
docker-compose logs -f
```

### 4. Verify Deployment

The service should be accessible at:
- Attendance Service: http://localhost:8080
- Swagger UI: http://localhost:8080/swagger-ui.html

### 5. Database Initialization

The database will be automatically initialized on first run. You can verify the database connection:

```bash
# Connect to PostgreSQL container
docker exec -it attendance-postgres psql -U postgres -d attendance_db

# List tables
\dt
```

### 6. Monitoring and Maintenance

#### View Service Logs
```bash
# View all service logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f attendance-service
```

#### Backup Database
```bash
# Create backup
docker exec attendance-postgres pg_dump -U postgres attendance_db > backup.sql

# Restore from backup
cat backup.sql | docker exec -i attendance-postgres psql -U postgres -d attendance_db
```

### 7. Scaling and Updates

#### Scale Service
```bash
# Scale to multiple instances
docker-compose up -d --scale attendance-service=3
```

#### Update Service
```bash
# Pull latest changes
git pull

# Rebuild and restart services
docker-compose down
docker-compose up -d --build
```

### 8. Troubleshooting

#### Common Issues

1. **Port Conflicts**
   - If port 8080 is already in use, modify the port mapping in docker-compose.yml
   - Check for running services: `netstat -tulpn | grep 8080`

2. **Database Connection Issues**
   - Verify PostgreSQL is running: `docker-compose ps`
   - Check logs: `docker-compose logs postgres`

3. **Memory Issues**
   - Adjust JAVA_OPTS in Dockerfile if needed
   - Monitor container memory: `docker stats`

#### Health Checks

```bash
# Check service health
curl http://localhost:8080/actuator/health

# Check container status
docker-compose ps
```

### 9. Security Considerations

1. Change default passwords in production
2. Use secure environment variables
3. Enable HTTPS in production
4. Configure proper firewall rules
5. Regular security updates

### 10. Production Deployment

For production deployment, consider:

1. Using a reverse proxy (Nginx/Traefik)
2. Setting up SSL/TLS
3. Implementing proper monitoring
4. Setting up automated backups
5. Using a container orchestration platform (Kubernetes)

## Support

For any deployment issues or questions, please contact the development team. 