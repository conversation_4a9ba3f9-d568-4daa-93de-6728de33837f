version: '3.8'

services:
  app:
    build: 
      context: .
      dockerfile: Dockerfile
    container_name: attendance-service
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - SPRING_REDIS_HOST=redis
      - SPRING_REDIS_PORT=6379
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
      - ./firebase-dev.json:/app/firebase-dev.json
      - ${USERPROFILE}/.m2:/root/.m2
    depends_on:
      - redis
    networks:
      - attendance-network
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    container_name: attendance-redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - attendance-network
    restart: unless-stopped

networks:
  attendance-network:
    driver: bridge

volumes:
  redis-data: 