# Reladomo Bitemporal Implementation for EMP_SHIFT_MAPPING

## Overview
This document describes the complete end-to-end implementation of Reladomo bitemporal data management for the EMP_SHIFT_MAPPING table in the rostering system.

## Implementation Components

### 1. Database Schema
The EMP_SHIFT_MAPPING table includes bitemporal columns:
- `BUSINESS_FROM` / `BUSINESS_TO`: When the fact was true in the real world
- `PROCESSING_FROM` / `PROCESSING_TO`: When the fact was recorded in the system
- Standard audit columns: `CREATED_BY`, `CREATION_TIME`, `UPDATED_BY`, `UPDATION_TIME`

### 2. Reladomo Configuration Files

#### EmpShiftMapping.xml
```xml
<MithraObject>
    <PackageName>com.stpl.tech.attendance.domain</PackageName>
    <ClassName>EmpShiftMapping</ClassName>
    <DefaultTable>EMP_SHIFT_MAPPING</DefaultTable>

    <!-- Bitemporal Configuration -->
    <AsOfAttribute name="businessDate" fromColumnName="BUSINESS_FROM" toColumnName="BUSINESS_TO"
                   toIsInclusive="false"
                   isProcessingDate="false"
                   infinityDate="[java.sql.Timestamp.valueOf(&quot;9999-12-01 23:59:00.000&quot;)]"
                   futureExpiringRowsExist="true"/>
    <AsOfAttribute name="processingDate" fromColumnName="PROCESSING_FROM" toColumnName="PROCESSING_TO"
                   toIsInclusive="false"
                   isProcessingDate="true"
                   infinityDate="[java.sql.Timestamp.valueOf(&quot;9999-12-01 23:59:00.000&quot;)]"
                   defaultIfNotSpecified="[java.sql.Timestamp.valueOf(&quot;9999-12-01 23:59:00.000&quot;)]"/>

    <!-- Primary Key -->
    <Attribute name="id" javaType="int" primaryKey="true" columnName="ID"/>

    <!-- Business Attributes -->
    <Attribute name="shiftId" javaType="int" columnName="SHIFT_ID"/>
    <Attribute name="empId" javaType="int" columnName="EMP_ID"/>
    <Attribute name="expectedStartDate" javaType="Timestamp" columnName="EXPECTED_START_DATE"/>
    <Attribute name="expectedEndDate" javaType="Timestamp" columnName="EXPECTED_END_DATE"/>
    <Attribute name="status" javaType="String" columnName="STATUS"/>

    <!-- Audit Attributes -->
    <Attribute name="createdBy" javaType="String" columnName="CREATED_BY"/>
    <Attribute name="creationTime" javaType="Timestamp" columnName="CREATION_TIME"/>
    <Attribute name="updatedBy" javaType="String" columnName="UPDATED_BY"/>
    <Attribute name="updationTime" javaType="Timestamp" columnName="UPDATION_TIME"/>
</MithraObject>
```

#### MithraClassList.xml
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Mithra>
    <MithraObjectResource name="domain/EmpShiftMapping"/>
</Mithra>
```

#### MithraRuntimeConfig.xml
```xml
<MithraRuntime>
    <connectionManager className="com.stpl.tech.attendance.config.MithraConnectionManager"/>
    
    <MithraObjectConfiguration className="com.stpl.tech.attendance.domain.EmpShiftMapping">
        <MithraObjectAttribute name="id" primaryKey="true"/>
        <MithraObjectAttribute name="empId"/>
        <MithraObjectAttribute name="shiftId"/>
        <MithraObjectAttribute name="expectedStartDate"/>
        <MithraObjectAttribute name="expectedEndDate"/>
        <MithraObjectAttribute name="status"/>
        <MithraObjectAttribute name="createdBy"/>
        <MithraObjectAttribute name="creationTime"/>
        <MithraObjectAttribute name="updatedBy"/>
        <MithraObjectAttribute name="updationTime"/>
    </MithraObjectConfiguration>
</MithraRuntime>
```

### 3. Maven Configuration

#### pom.xml additions:
```xml
<properties>
    <reladomo.version>17.0.1</reladomo.version>
</properties>

<dependencies>
    <dependency>
        <groupId>com.goldmansachs.reladomo</groupId>
        <artifactId>reladomo</artifactId>
        <version>${reladomo.version}</version>
    </dependency>
    <dependency>
        <groupId>com.goldmansachs.reladomo</groupId>
        <artifactId>reladomogen</artifactId>
        <version>${reladomo.version}</version>
    </dependency>
</dependencies>

<build>
    <plugins>
        <!-- Reladomo Code Generation -->
        <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-antrun-plugin</artifactId>
            <version>3.0.0</version>
            <executions>
                <execution>
                    <id>generateReladomo</id>
                    <phase>generate-sources</phase>
                    <goals>
                        <goal>run</goal>
                    </goals>
                    <configuration>
                        <target>
                            <taskdef name="gen-reladomo"
                                     classname="com.gs.fw.common.mithra.generator.MithraGenerator">
                                <classpath refid="maven.plugin.classpath"/>
                            </taskdef>
                            <gen-reladomo xml="${project.basedir}/src/main/resources/MithraClassList.xml"
                                          generatedDir="${project.build.directory}/generated-sources/reladomo"/>
                        </target>
                    </configuration>
                </execution>
            </executions>
            <dependencies>
                <dependency>
                    <groupId>com.goldmansachs.reladomo</groupId>
                    <artifactId>reladomogen</artifactId>
                    <version>${reladomo.version}</version>
                </dependency>
            </dependencies>
        </plugin>

        <!-- Add generated sources to compilation classpath -->
        <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>build-helper-maven-plugin</artifactId>
            <version>3.4.0</version>
            <executions>
                <execution>
                    <id>add-reladomo-sources</id>
                    <phase>generate-sources</phase>
                    <goals>
                        <goal>add-source</goal>
                    </goals>
                    <configuration>
                        <sources>
                            <source>${project.build.directory}/generated-sources/reladomo</source>
                        </sources>
                    </configuration>
                </execution>
            </executions>
        </plugin>
    </plugins>
</build>
```

### 4. Configuration Classes

#### ReladomoConfig.java
```java
@Configuration
@RequiredArgsConstructor
public class ReladomoConfig {

    private final MithraConnectionManager connectionManager;

    @PostConstruct
    public void initializeReladomo() {
        try {
            MithraManager mithraManager = MithraManagerProvider.getMithraManager();
            mithraManager.setJtaTransactionManagerProvider(null);
            
            ClassPathResource resource = new ClassPathResource("MithraRuntimeConfig.xml");
            try (InputStream configStream = resource.getInputStream()) {
                mithraManager.readConfiguration(configStream);
            }
            
            mithraManager.fullyInitialize();
        } catch (Exception e) {
            throw new RuntimeException("Reladomo initialization failed", e);
        }
    }

    @Bean
    public MithraManager mithraManager() {
        return MithraManagerProvider.getMithraManager();
    }
}
```

#### MithraConnectionManager.java
```java
@Component
public class MithraConnectionManager implements SourcelessConnectionManager {
    
    @Value("${spring.datasource.url}")
    private String url;
    
    @Value("${spring.datasource.username}")
    private String username;
    
    @Value("${spring.datasource.password}")
    private String password;
    
    @Override
    public Connection getConnection() {
        try {
            return DriverManager.getConnection(url, username, password);
        } catch (Exception e) {
            throw new RuntimeException("Failed to get DB connection", e);
        }
    }

    @Override
    public DatabaseType getDatabaseType() {
        return GenericDatabaseType.getInstance();
    }

    @Override
    public TimeZone getDatabaseTimeZone() {
        return TimeZone.getDefault();
    }

    @Override
    public BulkLoader createBulkLoader() throws BulkLoaderException {
        throw new UnsupportedOperationException("Bulk loading not implemented");
    }

    @Override
    public String getDatabaseIdentifier() {
        return "default";
    }
}
```

### 5. Service Implementation

#### ReladomoEmpShiftService.java (Interface)
```java
public interface ReladomoEmpShiftService {
    EmpShiftUpdateResponseDTO updateEmpShifts(EmpShiftUpdateRequestDTO request);
    List<EmpShiftMappingDTO> findCurrentShiftsByEmpId(Integer empId, LocalDateTime businessDate);
    List<EmpShiftMappingDTO> findShiftHistoryByEmpId(Integer empId);
}
```

#### ReladomoEmpShiftServiceImpl.java
The service implementation handles:
- Bitemporal object creation with business and processing dates
- Transaction management using MithraTransaction
- Validation of request parameters
- Conversion between Reladomo objects and DTOs
- Bulk update operations for multiple employee-shift combinations

### 6. API Endpoint

#### RosteringController.java
```java
@PutMapping("/shifts/emp-shift-update")
@Operation(summary = "Update employee shifts with Reladomo", 
           description = "Update employee shift assignments using Reladomo with bitemporal support")
public ResponseEntity<ApiResponse<EmpShiftUpdateResponseDTO>> updateEmpShift(
        @Valid @RequestBody EmpShiftUpdateRequestDTO request) {
    
    request.setUpdatedBy(String.valueOf(JwtContext.getInstance().getUserId()));
    EmpShiftUpdateResponseDTO result = reladomoEmpShiftService.updateEmpShifts(request);
    return success(result);
}
```

## Key Features

### 1. Bitemporal Support
- **Business Time**: Tracks when facts were true in the real world
- **Processing Time**: Tracks when facts were recorded in the system
- Supports historical corrections and future-dated changes

### 2. Transaction Management
- Uses MithraTransaction for ACID compliance
- Automatic rollback on errors
- Supports nested transactions

### 3. Validation
- Request parameter validation
- Business rule validation (date ranges, required fields)
- Data integrity checks

### 4. Performance
- Efficient bitemporal queries using Reladomo's optimized finder methods
- Bulk operations for multiple employee-shift combinations
- Caching support through Reladomo's cache management

## Usage Examples

### 1. Update Employee Shifts
```json
PUT /api/v1/roster/shifts/emp-shift-update
{
    "empIds": [1, 2, 3],
    "shiftIds": [101, 102],
    "businessFrom": "2024-01-01T00:00:00",
    "businessTo": "2024-01-31T23:59:59",
    "expectedArrivalTime": "2024-01-01T09:00:00"
}
```

### 2. Query Current Shifts
```java
List<EmpShiftMappingDTO> currentShifts = reladomoEmpShiftService
    .findCurrentShiftsByEmpId(empId, LocalDateTime.now());
```

### 3. Query Historical Data
```java
List<EmpShiftMappingDTO> history = reladomoEmpShiftService
    .findShiftHistoryByEmpId(empId);
```

## Benefits

1. **Temporal Accuracy**: Complete audit trail of all changes with precise timing
2. **Historical Queries**: Query data as it existed at any point in time
3. **Data Integrity**: Bitemporal constraints ensure data consistency
4. **Performance**: Optimized for temporal queries and bulk operations
5. **Flexibility**: Support for corrections, backdating, and future changes

## Testing

The implementation includes comprehensive tests:
- Unit tests for service methods
- Integration tests for end-to-end functionality
- Validation tests for error scenarios
- Performance tests for bulk operations

## Deployment Notes

1. Ensure database schema includes all bitemporal columns
2. Configure connection pooling for optimal performance
3. Set appropriate cache sizes based on data volume
4. Monitor transaction logs for performance tuning
5. Regular backup of bitemporal data for compliance

## Troubleshooting

### Common Issues:
1. **"Can't find symbol" errors**: Ensure Reladomo code generation runs before compilation
2. **Connection errors**: Verify database configuration and credentials
3. **Transaction timeouts**: Adjust transaction timeout settings for large bulk operations
4. **Memory issues**: Configure appropriate heap size for large datasets

### Debug Tips:
1. Enable Reladomo SQL logging for query analysis
2. Use transaction debugging for complex operations
3. Monitor cache hit ratios for performance optimization
4. Check bitemporal date ranges for data consistency issues
