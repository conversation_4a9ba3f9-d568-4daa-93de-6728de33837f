# HTTP Service Usage Guide

This guide explains how to use the centralized HTTP service for making HTTP requests in the application.

## Table of Contents
1. [Basic Usage](#basic-usage)
2. [Timeout Options](#timeout-options)
3. [Headers and Query Parameters](#headers-and-query-parameters)
4. [Error Handling](#error-handling)
5. [Best Practices](#best-practices)
6. [Examples](#examples)

## Basic Usage

The HTTP service provides three types of methods for each HTTP operation:
- Default timeout methods (using application default timeouts)
- Custom timeout methods (specify your own timeout)
- No timeout methods (for long-running operations)

### Default Timeout Methods

```java
@Service
public class ExampleService {
    private final HttpService httpService;

    public ExampleService(HttpService httpService) {
        this.httpService = httpService;
    }

    // Simple GET request
    public UserDTO getUser(String userId) {
        return httpService.get("/api/users/" + userId, UserDTO.class)
                .block();
    }

    // POST request with request body
    public ResponseDTO createUser(UserDTO user) {
        return httpService.post("/api/users", user, ResponseDTO.class)
                .block();
    }
}
```

## Timeout Options

### 1. Default Timeout
Uses the application's default timeout settings (configured in application.properties):
```properties
http.client.default.connect-timeout=5000
http.client.default.read-timeout=5000
http.client.default.write-timeout=5000
http.client.default.response-timeout=5000
```

### 2. Custom Timeout
Specify a custom timeout for specific requests:

```java
// GET request with 10 second timeout
public UserDTO getUserWithTimeout(String userId) {
    return httpService.getWithTimeout("/api/users/" + userId, UserDTO.class, 10000)
            .block();
}

// POST request with 30 second timeout
public ResponseDTO createUserWithTimeout(UserDTO user) {
    return httpService.postWithTimeout("/api/users", user, ResponseDTO.class, 30000)
            .block();
}
```

### 3. No Timeout
For long-running operations:

```java
// Long-running POST request without timeout
public ResponseDTO processLargeFile(FileDTO file) {
    return httpService.postWithoutTimeout("/api/process", file, ResponseDTO.class)
            .block();
}
```

## Headers and Query Parameters

### Headers
```java
Map<String, String> headers = new HashMap<>();
headers.put("Authorization", "Bearer token");
headers.put("X-Custom-Header", "value");

// GET with headers
httpService.get("/api/resource", ResponseDTO.class, headers);

// POST with headers
httpService.post("/api/resource", request, ResponseDTO.class, headers);
```

### Query Parameters
```java
Map<String, String> queryParams = new HashMap<>();
queryParams.put("page", "1");
queryParams.put("size", "10");

// GET with query parameters
httpService.get("/api/users", UserDTO.class, headers, queryParams);
```

## Error Handling

The HTTP service provides centralized error handling:

```java
try {
    ResponseDTO response = httpService.post("/api/resource", request, ResponseDTO.class)
            .block();
} catch (HttpServiceException e) {
    // Handle HTTP-specific errors
    log.error("HTTP request failed: {}", e.getMessage());
} catch (Exception e) {
    // Handle other errors
    log.error("Unexpected error: {}", e.getMessage());
}
```

## Best Practices

1. **Timeout Selection**
   - Use default timeouts for standard API calls
   - Use custom timeouts for known long-running operations
   - Use no-timeout methods only when necessary

2. **Error Handling**
   - Always handle HttpServiceException
   - Log errors with appropriate context
   - Consider retry mechanisms for transient failures

3. **Headers Management**
   - Use constants for common headers
   - Validate header values before sending
   - Keep sensitive information in headers secure

4. **Query Parameters**
   - Validate parameter values
   - Use URL encoding for special characters
   - Keep parameter names consistent

## Examples

### Complete Example with All Features

```java
@Service
@Slf4j
public class UserService {
    private final HttpService httpService;
    
    public UserService(HttpService httpService) {
        this.httpService = httpService;
    }

    public UserDTO getUserWithRetry(String userId) {
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Bearer " + getAuthToken());
        
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("include", "profile,settings");

        try {
            return httpService.getWithTimeout(
                "/api/users/" + userId,
                UserDTO.class,
                headers,
                queryParams,
                10000  // 10 second timeout
            ).block();
        } catch (HttpServiceException e) {
            log.error("Failed to fetch user: {}", e.getMessage());
            throw new UserServiceException("Failed to fetch user", e);
        }
    }

    public ResponseDTO createUserWithValidation(UserDTO user) {
        validateUser(user);
        
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Bearer " + getAuthToken());
        headers.put("X-Request-ID", UUID.randomUUID().toString());

        try {
            return httpService.post(
                "/api/users",
                user,
                ResponseDTO.class,
                headers
            ).block();
        } catch (HttpServiceException e) {
            log.error("Failed to create user: {}", e.getMessage());
            throw new UserServiceException("Failed to create user", e);
        }
    }
}
```

### Long-Running Operation Example

```java
@Service
public class FileProcessingService {
    private final HttpService httpService;

    public FileProcessingService(HttpService httpService) {
        this.httpService = httpService;
    }

    public ProcessingResult processLargeFile(FileDTO file) {
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Bearer " + getAuthToken());
        headers.put("X-File-Size", String.valueOf(file.getSize()));

        // Use no-timeout method for long-running operation
        return httpService.postWithoutTimeout(
            "/api/process-file",
            file,
            ProcessingResult.class,
            headers
        ).block();
    }
}
```

## Configuration

Configure default timeouts in `application.properties`:

```properties
# Default timeouts (in milliseconds)
http.client.default.connect-timeout=5000
http.client.default.read-timeout=5000
http.client.default.write-timeout=5000
http.client.default.response-timeout=5000

# Logging level for HTTP service
logging.level.com.stpl.tech.attendance.service.impl.HttpServiceImpl=DEBUG
```

## Support

For any questions or issues regarding the HTTP service, please contact the development team. 