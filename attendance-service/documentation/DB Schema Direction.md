
-- V1.0.4__create_biometric_template_table.sql
CREATE TABLE BIOMETRIC_TEMPLATE (
    B<PERSON><PERSON>TRIC_TEMPLATE_ID INTEGER NOT NULL AUTO_INCREMENT,
    EMP_ID INTEGER NOT NULL,
    UNIT_ID INTEGER NOT NULL, -- Unit where template was registered
    EXTERNAL_TEMPLATE_ID VARCHAR(100) NULL, -- NULL until approved
    TEMPLATE_VERSION VARCHAR(50) NOT NULL,
    TEMPLATE_DATA BLOB NOT NULL,
    ENROLLMENT_DEVICE_ID VARCHAR(100) NULL,
    ENROLLMENT_DEVICE_TYPE VARCHAR(50) NULL, -- MO<PERSON>LE, TABLET, <PERSON><PERSON>SK, etc.
    ENROLLMENT_DEVICE_MODEL VARCHAR(100) NULL,
    ENROLLMENT_DEVICE_OS VARCHAR(50) NULL,
    ENROLLMENT_DEVICE_OS_VERSION VARCHAR(50) NULL,
    <PERSON><PERSON><PERSON><PERSON>ENT_DATE TIMESTAMP NOT NULL,
    <PERSON><PERSON><PERSON><PERSON>ENT_IP_ADDRESS VARCHAR(50) NULL,
    <PERSON><PERSON><PERSON>LMENT_LOCATION_LAT DECIMAL(10,8) NULL,
    ENROLLMENT_LOCATION_LONG DECIMAL(11,8) NULL,
    APPROVAL_STATUS VARCHAR(20) DEFAULT 'PENDING', -- PENDING, APPROVED, REJECTED
    APPROVER_ID VARCHAR(36) NULL,
    APPROVAL_DATE TIMESTAMP NULL,
    REJECTION_REASON TEXT NULL,
    BIOMETRIC_TEMPLATE_STATUS VARCHAR(10) DEFAULT 'ACTIVE',
    CREATED_DATE TIMESTAMP NOT NULL,
    CREATED_BY VARCHAR(36) NOT NULL,
    UPDATED_DATE TIMESTAMP NOT NULL,
    UPDATED_BY VARCHAR(36) NOT NULL,
    DELETED_DATE TIMESTAMP NULL,
    DELETED_BY VARCHAR(36) NULL,
    PRIMARY KEY (BIOMETRIC_TEMPLATE_ID)
);

CREATE INDEX IDX_BT_EMP_ID ON BIOMETRIC_TEMPLATE(EMP_ID) USING BTREE;
CREATE INDEX IDX_BT_UNIT_ID ON BIOMETRIC_TEMPLATE(UNIT_ID) USING BTREE;
CREATE INDEX IDX_BT_EXTERNAL_TEMPLATE_ID ON BIOMETRIC_TEMPLATE(EXTERNAL_TEMPLATE_ID) USING BTREE;
CREATE INDEX IDX_BT_APPROVAL_STATUS ON BIOMETRIC_TEMPLATE(APPROVAL_STATUS) USING BTREE;

-- V1.0.5__create_biometric_template_metrics_table.sql
CREATE TABLE BIOMETRIC_TEMPLATE_METRICS (
    METRIC_ID INTEGER NOT NULL AUTO_INCREMENT,
    BIOMETRIC_TEMPLATE_ID INTEGER NOT NULL,
    TEMPLATE_QUALITY_SCORE DECIMAL(5,2) NULL,
    TEMPLATE_FEATURES_COUNT INT NULL,
    ENROLLMENT_LIGHTING_CONDITION VARCHAR(50) NULL, -- GOOD, AVERAGE, POOR
    LAST_UPDATE_DATE TIMESTAMP NULL,
    LAST_SUCCESSFUL_MATCH_DATE TIMESTAMP NULL,
    SUCCESSFUL_MATCH_COUNT INT DEFAULT 0,
    FAILED_MATCH_COUNT INT DEFAULT 0,
    AVERAGE_MATCH_SCORE DECIMAL(5,2) NULL,
    BIOMETRIC_TEMPLATE_METRICS_STATUS VARCHAR(10) DEFAULT 'ACTIVE',
    CREATED_DATE TIMESTAMP NOT NULL,
    CREATED_BY VARCHAR(36) NOT NULL,
    UPDATED_DATE TIMESTAMP NOT NULL,
    UPDATED_BY VARCHAR(36) NOT NULL,
    DELETED_DATE TIMESTAMP NULL,
    DELETED_BY VARCHAR(36) NULL,
    PRIMARY KEY (METRIC_ID),
    CONSTRAINT FK_BTM_BIOMETRIC_TEMPLATE_ID FOREIGN KEY (BIOMETRIC_TEMPLATE_ID) REFERENCES BIOMETRIC_TEMPLATE(BIOMETRIC_TEMPLATE_ID)
);

CREATE INDEX IDX_BTM_BIOMETRIC_TEMPLATE_ID ON BIOMETRIC_TEMPLATE_METRICS(BIOMETRIC_TEMPLATE_ID) USING BTREE;
CREATE INDEX IDX_BTM_TEMPLATE_QUALITY ON BIOMETRIC_TEMPLATE_METRICS(TEMPLATE_QUALITY_SCORE) USING BTREE;
CREATE INDEX IDX_BTM_MATCH_METRICS ON BIOMETRIC_TEMPLATE_METRICS(SUCCESSFUL_MATCH_COUNT, FAILED_MATCH_COUNT) USING BTREE;

-- V1.0.5__create_user_cafe_assignment_table.sql
CREATE TABLE USER_UNIT_ASSIGNMENT (
    USER_UNIT_ASSIGNMENT_ID VARCHAR(36) NOT NULL,
    EMP_ID VARCHAR(36) NOT NULL,
    UNIT_ID VARCHAR(36) NOT NULL,
    ASSIGNMENT_TYPE VARCHAR(20) NOT NULL, -- FIXED, ROTATING, MULTI_LOCATION, MOBILE
    IS_PRIMARY BOOLEAN DEFAULT FALSE,
    START_DATE DATE NOT NULL,
    END_DATE DATE NULL,
    USER_UNIT_ASSIGNMENT_STATUS VARCHAR(10) DEFAULT 'ACTIVE',
    CREATED_DATE DATETIME NOT NULL,
    CREATED_BY VARCHAR(36) NOT NULL,
    UPDATED_DATE DATETIME NOT NULL,
    UPDATED_BY VARCHAR(36) NOT NULL,
    DELETED_DATE DATETIME NULL,
    DELETED_BY VARCHAR(36) NULL,
    PRIMARY KEY (USER_UNIT_ASSIGNMENT_ID),
    CONSTRAINT FK_UUA_EMP_ID FOREIGN KEY (EMP_ID) REFERENCES USER(EMP_ID),
    CONSTRAINT FK_UUA_UNIT_ID FOREIGN KEY (UNIT_ID) REFERENCES UNIT(UNIT_ID)
);

CREATE INDEX IDX_UUA_EMP_ID ON USER_UNIT_ASSIGNMENT(EMP_ID) USING BTREE;
CREATE INDEX IDX_UUA_UNIT_ID ON USER_UNIT_ASSIGNMENT(UNIT_ID) USING BTREE;
CREATE INDEX IDX_UUA_ASSIGNMENT_TYPE ON USER_UNIT_ASSIGNMENT(ASSIGNMENT_TYPE) USING BTREE;
CREATE INDEX IDX_UUA_STATUS ON USER_UNIT_ASSIGNMENT(USER_UNIT_ASSIGNMENT_STATUS) USING BTREE;

-- V1.0.6__create_transfer_request_table.sql
CREATE TABLE TRANSFER_REQUEST (
    TRANSFER_REQUEST_ID VARCHAR(36) NOT NULL,
    EMP_ID VARCHAR(36) NOT NULL,
    SOURCE_UNIT_ID VARCHAR(36) NOT NULL,
    DESTINATION_UNIT_ID VARCHAR(36) NOT NULL,
    TRANSFER_TYPE VARCHAR(20) NOT NULL, -- TEMPORARY, PERMANENT
    START_DATE DATE NOT NULL,
    END_DATE DATE NULL,
    REASON TEXT NULL,
    INITIATOR_ID VARCHAR(36) NOT NULL,
    DESTINATION_MANAGER_ID VARCHAR(36) NOT NULL,
    SOURCE_MANAGER_ID VARCHAR(36) NOT NULL,
    HR_APPROVER_ID VARCHAR(36) NULL,
    REQUEST_STATUS VARCHAR(20) DEFAULT 'PENDING', -- PENDING, APPROVED, REJECTED, CANCELLED
    TRANSFER_REQUEST_STATUS VARCHAR(10) DEFAULT 'ACTIVE',
    CREATED_DATE DATETIME NOT NULL,
    CREATED_BY VARCHAR(36) NOT NULL,
    UPDATED_DATE DATETIME NOT NULL,
    UPDATED_BY VARCHAR(36) NOT NULL,
    DELETED_DATE DATETIME NULL,
    DELETED_BY VARCHAR(36) NULL,
    PRIMARY KEY (TRANSFER_REQUEST_ID),
    CONSTRAINT FK_TR_EMP_ID FOREIGN KEY (EMP_ID) REFERENCES USER(EMP_ID),
    CONSTRAINT FK_TR_SOURCE_UNIT_ID FOREIGN KEY (SOURCE_UNIT_ID) REFERENCES UNIT(UNIT_ID),
    CONSTRAINT FK_TR_DEST_UNIT_ID FOREIGN KEY (DESTINATION_UNIT_ID) REFERENCES UNIT(UNIT_ID),
    CONSTRAINT FK_TR_INITIATOR_ID FOREIGN KEY (INITIATOR_ID) REFERENCES USER(EMP_ID),
    CONSTRAINT FK_TR_DEST_MGR_ID FOREIGN KEY (DESTINATION_MANAGER_ID) REFERENCES USER(EMP_ID),
    CONSTRAINT FK_TR_SOURCE_MGR_ID FOREIGN KEY (SOURCE_MANAGER_ID) REFERENCES USER(EMP_ID),
    CONSTRAINT FK_TR_HR_APPROVER_ID FOREIGN KEY (HR_APPROVER_ID) REFERENCES USER(EMP_ID)
);

CREATE INDEX IDX_TR_EMP_ID ON TRANSFER_REQUEST(EMP_ID) USING BTREE;
CREATE INDEX IDX_TR_SOURCE_UNIT_ID ON TRANSFER_REQUEST(SOURCE_UNIT_ID) USING BTREE;
CREATE INDEX IDX_TR_DEST_UNIT_ID ON TRANSFER_REQUEST(DESTINATION_UNIT_ID) USING BTREE;
CREATE INDEX IDX_TR_REQUEST_STATUS ON TRANSFER_REQUEST(REQUEST_STATUS) USING BTREE;
CREATE INDEX IDX_TR_TRANSFER_TYPE ON TRANSFER_REQUEST(TRANSFER_TYPE) USING BTREE;

-- V1.0.7__create_approval_table.sql
CREATE TABLE APPROVAL (
    APPROVAL_ID VARCHAR(36) NOT NULL,
    ENTITY_TYPE VARCHAR(50) NOT NULL, -- TRANSFER_REQUEST, etc.
    ENTITY_ID VARCHAR(36) NOT NULL,
    APPROVER_ID VARCHAR(36) NOT NULL,
    APPROVAL_STATUS VARCHAR(20) DEFAULT 'PENDING', -- PENDING, APPROVED, REJECTED
    COMMENTS TEXT NULL,
    APPROVAL_STATUS VARCHAR(10) DEFAULT 'ACTIVE',
    CREATED_DATE DATETIME NOT NULL,
    CREATED_BY VARCHAR(36) NOT NULL,
    UPDATED_DATE DATETIME NOT NULL,
    UPDATED_BY VARCHAR(36) NOT NULL,
    DELETED_DATE DATETIME NULL,
    DELETED_BY VARCHAR(36) NULL,
    PRIMARY KEY (APPROVAL_ID),
    CONSTRAINT FK_APR_APPROVER_ID FOREIGN KEY (APPROVER_ID) REFERENCES USER(EMP_ID)
);

CREATE INDEX IDX_APR_ENTITY_TYPE_ID ON APPROVAL(ENTITY_TYPE, ENTITY_ID) USING BTREE;
CREATE INDEX IDX_APR_APPROVER_ID ON APPROVAL(APPROVER_ID) USING BTREE;
CREATE INDEX IDX_APR_APPROVAL_STATUS ON APPROVAL(APPROVAL_STATUS) USING BTREE;

-- V1.0.8__create_shift_table.sql
CREATE TABLE SHIFT (
    SHIFT_ID VARCHAR(36) NOT NULL,
    SHIFT_NAME VARCHAR(50) NOT NULL,
    START_TIME TIME NOT NULL,
    END_TIME TIME NOT NULL,
    SHIFT_STATUS VARCHAR(10) DEFAULT 'ACTIVE',
    CREATED_DATE DATETIME NOT NULL,
    CREATED_BY VARCHAR(36) NOT NULL,
    UPDATED_DATE DATETIME NOT NULL,
    UPDATED_BY VARCHAR(36) NOT NULL,
    DELETED_DATE DATETIME NULL,
    DELETED_BY VARCHAR(36) NULL,
    PRIMARY KEY (SHIFT_ID)
);

-- V1.0.9__create_roster_table.sql
CREATE TABLE ROSTER (
    ROSTER_ID VARCHAR(36) NOT NULL,
    EMP_ID VARCHAR(36) NOT NULL,
    UNIT_ID VARCHAR(36) NOT NULL,
    SHIFT_ID VARCHAR(36) NOT NULL,
    ROSTER_DATE DATE NOT NULL,
    ROSTER_STATUS VARCHAR(10) DEFAULT 'ACTIVE',
    CREATED_DATE DATETIME NOT NULL,
    CREATED_BY VARCHAR(36) NOT NULL,
    UPDATED_DATE DATETIME NOT NULL,
    UPDATED_BY VARCHAR(36) NOT NULL,
    DELETED_DATE DATETIME NULL,
    DELETED_BY VARCHAR(36) NULL,
    PRIMARY KEY (ROSTER_ID),
    CONSTRAINT FK_ROSTER_EMP_ID FOREIGN KEY (EMP_ID) REFERENCES USER(EMP_ID),
    CONSTRAINT FK_ROSTER_UNIT_ID FOREIGN KEY (UNIT_ID) REFERENCES UNIT(UNIT_ID),
    CONSTRAINT FK_ROSTER_SHIFT_ID FOREIGN KEY (SHIFT_ID) REFERENCES SHIFT(SHIFT_ID),
    CONSTRAINT UQ_USER_UNIT_DATE_SHIFT UNIQUE (EMP_ID, UNIT_ID, ROSTER_DATE, SHIFT_ID)
);

CREATE INDEX IDX_ROSTER_EMP_ID ON ROSTER(EMP_ID) USING BTREE;
CREATE INDEX IDX_ROSTER_UNIT_ID ON ROSTER(UNIT_ID) USING BTREE;
CREATE INDEX IDX_ROSTER_DATE ON ROSTER(ROSTER_DATE) USING BTREE;
CREATE INDEX IDX_ROSTER_SHIFT_ID ON ROSTER(SHIFT_ID) USING BTREE;

-- V1.0.10__create_attendance_event_table.sql
CREATE TABLE ATTENDANCE_EVENT (
    ATTENDANCE_EVENT_ID VARCHAR(36) NOT NULL,
    EMP_ID VARCHAR(36) NOT NULL,
    UNIT_ID VARCHAR(36) NOT NULL,
    SHIFT_ID VARCHAR(36) NOT NULL,
    EVENT_TYPE VARCHAR(20) NOT NULL, -- CHECK_IN, CHECK_OUT, INTRA_DAY
    EVENT_TIME DATETIME NOT NULL,
    LOCATION_LAT DECIMAL(10,8) NOT NULL,
    LOCATION_LONG DECIMAL(11,8) NOT NULL,
    DEVICE_ID VARCHAR(100) NULL,
    DEVICE_TYPE VARCHAR(50) NULL, -- MOBILE, TABLET, KIOSK, etc.
    DEVICE_MODEL VARCHAR(100) NULL,
    DEVICE_OS VARCHAR(50) NULL,
    DEVICE_OS_VERSION VARCHAR(50) NULL,
    IP_ADDRESS VARCHAR(50) NULL,
    NETWORK_TYPE VARCHAR(20) NULL, -- WIFI, 4G, 5G, etc.
    BIOMETRIC_TEMPLATE_ID VARCHAR(36) NULL,
    CONFIDENCE_SCORE DECIMAL(5,2) NULL,
    ATTENDANCE_EVENT_STATUS VARCHAR(10) DEFAULT 'ACTIVE',
    CREATED_DATE DATETIME NOT NULL,
    CREATED_BY VARCHAR(36) NOT NULL,
    UPDATED_DATE DATETIME NOT NULL,
    UPDATED_BY VARCHAR(36) NOT NULL,
    DELETED_DATE DATETIME NULL,
    DELETED_BY VARCHAR(36) NULL,
    PRIMARY KEY (ATTENDANCE_EVENT_ID),
    CONSTRAINT FK_AE_EMP_ID FOREIGN KEY (EMP_ID) REFERENCES USER(EMP_ID),
    CONSTRAINT FK_AE_UNIT_ID FOREIGN KEY (UNIT_ID) REFERENCES UNIT(UNIT_ID),
    CONSTRAINT FK_AE_SHIFT_ID FOREIGN KEY (SHIFT_ID) REFERENCES SHIFT(SHIFT_ID),
    CONSTRAINT FK_AE_BIOMETRIC_TEMPLATE_ID FOREIGN KEY (BIOMETRIC_TEMPLATE_ID) REFERENCES BIOMETRIC_TEMPLATE(BIOMETRIC_TEMPLATE_ID)
);

CREATE INDEX IDX_AE_EMP_ID ON ATTENDANCE_EVENT(EMP_ID) USING BTREE;
CREATE INDEX IDX_AE_UNIT_ID ON ATTENDANCE_EVENT(UNIT_ID) USING BTREE;
CREATE INDEX IDX_AE_EVENT_TIME ON ATTENDANCE_EVENT(EVENT_TIME) USING BTREE;
CREATE INDEX IDX_AE_EVENT_TYPE ON ATTENDANCE_EVENT(EVENT_TYPE) USING BTREE;
CREATE INDEX IDX_AE_SHIFT_ID ON ATTENDANCE_EVENT(SHIFT_ID) USING BTREE;
CREATE INDEX IDX_AE_IP_ADDRESS ON ATTENDANCE_EVENT(IP_ADDRESS) USING BTREE;

-- V1.0.11__create_attendance_table.sql
CREATE TABLE ATTENDANCE (
    ATTENDANCE_ID VARCHAR(36) NOT NULL,
    EMP_ID VARCHAR(36) NOT NULL,
    UNIT_ID VARCHAR(36) NOT NULL,
    SHIFT_ID VARCHAR(36) NOT NULL,
    ATTENDANCE_DATE DATE NOT NULL,
    CHECK_IN_TIME DATETIME NOT NULL,
    CHECK_OUT_TIME DATETIME NULL,
    TOTAL_HOURS DECIMAL(5,2) NULL,
    CHECK_IN_EVENT_ID VARCHAR(36) NOT NULL,
    CHECK_OUT_EVENT_ID VARCHAR(36) NULL,
    MEAL_ELIGIBLE BOOLEAN DEFAULT FALSE,
    ATTENDANCE_STATUS VARCHAR(10) DEFAULT 'ACTIVE',
    CREATED_DATE DATETIME NOT NULL,
    CREATED_BY VARCHAR(36) NOT NULL,
    UPDATED_DATE DATETIME NOT NULL,
    UPDATED_BY VARCHAR(36) NOT NULL,
    DELETED_DATE DATETIME NULL,
    DELETED_BY VARCHAR(36) NULL,
    PRIMARY KEY (ATTENDANCE_ID),
    CONSTRAINT FK_ATT_EMP_ID FOREIGN KEY (EMP_ID) REFERENCES USER(EMP_ID),
    CONSTRAINT FK_ATT_UNIT_ID FOREIGN KEY (UNIT_ID) REFERENCES UNIT(UNIT_ID),
    CONSTRAINT FK_ATT_SHIFT_ID FOREIGN KEY (SHIFT_ID) REFERENCES SHIFT(SHIFT_ID),
    CONSTRAINT FK_ATT_CHECK_IN_EVENT_ID FOREIGN KEY (CHECK_IN_EVENT_ID) REFERENCES ATTENDANCE_EVENT(ATTENDANCE_EVENT_ID),
    CONSTRAINT FK_ATT_CHECK_OUT_EVENT_ID FOREIGN KEY (CHECK_OUT_EVENT_ID) REFERENCES ATTENDANCE_EVENT(ATTENDANCE_EVENT_ID)
);

CREATE INDEX IDX_ATT_EMP_ID ON ATTENDANCE(EMP_ID) USING BTREE;
CREATE INDEX IDX_ATT_UNIT_ID ON ATTENDANCE(UNIT_ID) USING BTREE;
CREATE INDEX IDX_ATT_DATE ON ATTENDANCE(ATTENDANCE_DATE) USING BTREE;
CREATE INDEX IDX_ATT_SHIFT_ID ON ATTENDANCE(SHIFT_ID) USING BTREE;

-- V1.0.12__create_attendance_failure_event_table.sql
CREATE TABLE ATTENDANCE_FAILURE_EVENT (
    ATTENDANCE_FAILURE_EVENT_ID VARCHAR(36) NOT NULL,
    EMP_ID VARCHAR(36) NULL, -- Can be NULL if user couldn't be identified
    UNIT_ID VARCHAR(36) NOT NULL,
    DEVICE_ID VARCHAR(100) NULL,
    EVENT_TIME DATETIME NOT NULL,
    LOCATION_LAT DECIMAL(10,8) NOT NULL,
    LOCATION_LONG DECIMAL(11,8) NOT NULL,
    FAILURE_REASON VARCHAR(100) NOT NULL, -- NO_FACE_DETECTED, LOW_CONFIDENCE, MULTIPLE_FACES, etc.
    FAILURE_DETAILS TEXT NULL,
    CAPTURED_IMAGE_URL VARCHAR(255) NULL,
    CONFIDENCE_SCORE DECIMAL(5,2) NULL,
    ATTENDANCE_FAILURE_EVENT_STATUS VARCHAR(10) DEFAULT 'ACTIVE',
    CREATED_DATE DATETIME NOT NULL,
    CREATED_BY VARCHAR(36) NOT NULL,
    UPDATED_DATE DATETIME NOT NULL,
    UPDATED_BY VARCHAR(36) NOT NULL,
    DELETED_DATE DATETIME NULL,
    DELETED_BY VARCHAR(36) NULL,
    PRIMARY KEY (ATTENDANCE_FAILURE_EVENT_ID),
    CONSTRAINT FK_AFE_EMP_ID FOREIGN KEY (EMP_ID) REFERENCES USER(EMP_ID),
    CONSTRAINT FK_AFE_UNIT_ID FOREIGN KEY (UNIT_ID) REFERENCES UNIT(UNIT_ID)
);

CREATE INDEX IDX_AFE_EMP_ID ON ATTENDANCE_FAILURE_EVENT(EMP_ID) USING BTREE;
CREATE INDEX IDX_AFE_UNIT_ID ON ATTENDANCE_FAILURE_EVENT(UNIT_ID) USING BTREE;
CREATE INDEX IDX_AFE_EVENT_TIME ON ATTENDANCE_FAILURE_EVENT(EVENT_TIME) USING BTREE;
CREATE INDEX IDX_AFE_FAILURE_REASON ON ATTENDANCE_FAILURE_EVENT(FAILURE_REASON) USING BTREE;
CREATE TABLE GROOMING_STANDARD (
    GROOMING_STANDARD_ID VARCHAR(36) NOT NULL,
    STANDARD_NAME VARCHAR(100) NOT NULL,
    DESCRIPTION TEXT NOT NULL,
    GROOMING_STANDARD_STATUS VARCHAR(10) DEFAULT 'ACTIVE',
    CREATED_DATE DATETIME NOT NULL,
    CREATED_BY VARCHAR(36) NOT NULL,
    UPDATED_DATE DATETIME NOT NULL,
    UPDATED_BY VARCHAR(36) NOT NULL,
    DELETED_DATE DATETIME NULL,
    DELETED_BY VARCHAR(36) NULL,
    PRIMARY KEY (GROOMING_STANDARD_ID)
);

-- V1.0.12__create_grooming_evaluation_table.sql
CREATE TABLE GROOMING_EVALUATION (
    GROOMING_EVALUATION_ID VARCHAR(36) NOT NULL,
    EMP_ID VARCHAR(36) NOT NULL,
    EVALUATOR_ID VARCHAR(36) NOT NULL,
    EVALUATION_DATE DATE NOT NULL,
    PHOTO_URL VARCHAR(255) NULL,
    COMMENTS TEXT NULL,
    OVERALL_RESULT VARCHAR(20) NOT NULL, -- PASS, FAIL
    GROOMING_EVALUATION_STATUS VARCHAR(10) DEFAULT 'ACTIVE',
    CREATED_DATE DATETIME NOT NULL,
    CREATED_BY VARCHAR(36) NOT NULL,
    UPDATED_DATE DATETIME NOT NULL,
    UPDATED_BY VARCHAR(36) NOT NULL,
    DELETED_DATE DATETIME NULL,
    DELETED_BY VARCHAR(36) NULL,
    PRIMARY KEY (GROOMING_EVALUATION_ID),
    CONSTRAINT FK_GE_EMP_ID FOREIGN KEY (EMP_ID) REFERENCES USER(EMP_ID),
    CONSTRAINT FK_GE_EVALUATOR_ID FOREIGN KEY (EVALUATOR_ID) REFERENCES USER(EMP_ID)
);

CREATE INDEX IDX_GE_EMP_ID ON GROOMING_EVALUATION(EMP_ID) USING BTREE;
CREATE INDEX IDX_GE_EVALUATOR_ID ON GROOMING_EVALUATION(EVALUATOR_ID) USING BTREE;
CREATE INDEX IDX_GE_EVALUATION_DATE ON GROOMING_EVALUATION(EVALUATION_DATE) USING BTREE;
CREATE INDEX IDX_GE_OVERALL_RESULT ON GROOMING_EVALUATION(OVERALL_RESULT) USING BTREE;

-- V1.0.13__create_grooming_evaluation_detail_table.sql
CREATE TABLE GROOMING_EVALUATION_DETAIL (
    GROOMING_EVALUATION_DETAIL_ID VARCHAR(36) NOT NULL,
    GROOMING_EVALUATION_ID VARCHAR(36) NOT NULL,
    GROOMING_STANDARD_ID VARCHAR(36) NOT NULL,
    RESULT VARCHAR(20) NOT NULL, -- PASS, FAIL
    COMMENTS TEXT NULL,
    GROOMING_EVALUATION_DETAIL_STATUS VARCHAR(10) DEFAULT 'ACTIVE',
    CREATED_DATE DATETIME NOT NULL,
    CREATED_BY VARCHAR(36) NOT NULL,
    UPDATED_DATE DATETIME NOT NULL,
    UPDATED_BY VARCHAR(36) NOT NULL,
    DELETED_DATE DATETIME NULL,
    DELETED_BY VARCHAR(36) NULL,
    PRIMARY KEY (GROOMING_EVALUATION_DETAIL_ID),
    CONSTRAINT FK_GED_GE_ID FOREIGN KEY (GROOMING_EVALUATION_ID) REFERENCES GROOMING_EVALUATION(GROOMING_EVALUATION_ID),
    CONSTRAINT FK_GED_GS_ID FOREIGN KEY (GROOMING_STANDARD_ID) REFERENCES GROOMING_STANDARD(GROOMING_STANDARD_ID)
);

CREATE INDEX IDX_GED_GE_ID ON GROOMING_EVALUATION_DETAIL(GROOMING_EVALUATION_ID) USING BTREE;
CREATE INDEX IDX_GED_GS_ID ON GROOMING_EVALUATION_DETAIL(GROOMING_STANDARD_ID) USING BTREE;
CREATE INDEX IDX_GED_RESULT ON GROOMING_EVALUATION_DETAIL(RESULT) USING BTREE;

-- V1.0.14__create_notification_table.sql
CREATE TABLE NOTIFICATION (
    NOTIFICATION_ID VARCHAR(36) NOT NULL,
    EMP_ID VARCHAR(36) NOT NULL,
    NOTIFICATION_TYPE VARCHAR(50) NOT NULL,
    TITLE VARCHAR(100) NOT NULL,
    MESSAGE TEXT NOT NULL,
    ENTITY_TYPE VARCHAR(50) NULL,
    ENTITY_ID VARCHAR(36) NULL,
    IS_READ BOOLEAN DEFAULT FALSE,
    NOTIFICATION_STATUS VARCHAR(10) DEFAULT 'ACTIVE',
    CREATED_DATE DATETIME NOT NULL,
    CREATED_BY VARCHAR(36) NOT NULL,
    UPDATED_DATE DATETIME NOT NULL,
    UPDATED_BY VARCHAR(36) NOT NULL,
    DELETED_DATE DATETIME NULL,
    DELETED_BY VARCHAR(36) NULL,
    PRIMARY KEY (NOTIFICATION_ID),
    CONSTRAINT FK_NOTIF_EMP_ID FOREIGN KEY (EMP_ID) REFERENCES USER(EMP_ID)
);

CREATE INDEX IDX_NOTIF_EMP_ID ON NOTIFICATION(EMP_ID) USING BTREE;
CREATE INDEX IDX_NOTIF_IS_READ ON NOTIFICATION(IS_READ) USING BTREE;
CREATE INDEX IDX_NOTIF_ENTITY ON NOTIFICATION(ENTITY_TYPE, ENTITY_ID) USING BTREE;

-- V1.0.15__create_beat_plan_table.sql
CREATE TABLE BEAT_PLAN (
    BEAT_PLAN_ID VARCHAR(36) NOT NULL,
    MANAGER_ID VARCHAR(36) NOT NULL,
    PLAN_DATE DATE NOT NULL,
    BEAT_PLAN_STATUS VARCHAR(10) DEFAULT 'ACTIVE',
    CREATED_DATE DATETIME NOT NULL,
    CREATED_BY VARCHAR(36) NOT NULL,
    UPDATED_DATE DATETIME NOT NULL,
    UPDATED_BY VARCHAR(36) NOT NULL,
    DELETED_DATE DATETIME NULL,
    DELETED_BY VARCHAR(36) NULL,
    PRIMARY KEY (BEAT_PLAN_ID),
    CONSTRAINT FK_BP_MANAGER_ID FOREIGN KEY (MANAGER_ID) REFERENCES USER(EMP_ID)
);

CREATE INDEX IDX_BP_MANAGER_ID ON BEAT_PLAN(MANAGER_ID) USING BTREE;
CREATE INDEX IDX_BP_PLAN_DATE ON BEAT_PLAN(PLAN_DATE) USING BTREE;

-- V1.0.16__create_beat_plan_detail_table.sql
CREATE TABLE BEAT_PLAN_DETAIL (
    BEAT_PLAN_DETAIL_ID VARCHAR(36) NOT NULL,
    BEAT_PLAN_ID VARCHAR(36) NOT NULL,
    UNIT_ID VARCHAR(36) NOT NULL,
    VISIT_SEQUENCE INT NOT NULL,
    PLANNED_ARRIVAL_TIME TIME NOT NULL,
    PLANNED_DEPARTURE_TIME TIME NOT NULL,
    ACTUAL_ARRIVAL_TIME TIME NULL,
    ACTUAL_DEPARTURE_TIME TIME NULL,
    BEAT_PLAN_DETAIL_STATUS VARCHAR(10) DEFAULT 'ACTIVE',
    CREATED_DATE DATETIME NOT NULL,
    CREATED_BY VARCHAR(36) NOT NULL,
    UPDATED_DATE DATETIME NOT NULL,
    UPDATED_BY VARCHAR(36) NOT NULL,
    DELETED_DATE DATETIME NULL,
    DELETED_BY VARCHAR(36) NULL,
    PRIMARY KEY (BEAT_PLAN_DETAIL_ID),
    CONSTRAINT FK_BPD_BP_ID FOREIGN KEY (BEAT_PLAN_ID) REFERENCES BEAT_PLAN(BEAT_PLAN_ID),
    CONSTRAINT FK_BPD_UNIT_ID FOREIGN KEY (UNIT_ID) REFERENCES UNIT(UNIT_ID)
);

CREATE INDEX IDX_BPD_BP_ID ON BEAT_PLAN_DETAIL(BEAT_PLAN_ID) USING BTREE;
CREATE INDEX IDX_BPD_UNIT_ID ON BEAT_PLAN_DETAIL(UNIT_ID) USING BTREE;
CREATE INDEX IDX_BPD_VISIT_SEQUENCE ON BEAT_PLAN_DETAIL(VISIT_SEQUENCE) USING BTREE;

-- V1.0.17__create_device_table.sql
CREATE TABLE DEVICE (
    DEVICE_ID VARCHAR(36) NOT NULL,
    DEVICE_UNIQUE_ID VARCHAR(100) NOT NULL,
    DEVICE_TYPE VARCHAR(50) NOT NULL,
    DEVICE_MODEL VARCHAR(100) NULL,
    OWNER_TYPE VARCHAR(20) NOT NULL, -- USER, UNIT
    OWNER_ID VARCHAR(36) NOT NULL,
    DEVICE_STATUS VARCHAR(10) DEFAULT 'ACTIVE',
    CREATED_DATE DATETIME NOT NULL,
    CREATED_BY VARCHAR(36) NOT NULL,
    UPDATED_DATE DATETIME NOT NULL,
    UPDATED_BY VARCHAR(36) NOT NULL,
    DELETED_DATE DATETIME NULL,
    DELETED_BY VARCHAR(36) NULL,
    PRIMARY KEY (DEVICE_ID),
    CONSTRAINT UQ_DEVICE_UNIQUE_ID UNIQUE (DEVICE_UNIQUE_ID)
);

CREATE INDEX IDX_DEVICE_OWNER ON DEVICE(OWNER_TYPE, OWNER_ID) USING BTREE;
CREATE INDEX IDX_DEVICE_STATUS ON DEVICE(DEVICE_STATUS) USING BTREE;

-- V1.0.18__create_audit_log_table.sql
CREATE TABLE AUDIT_LOG (
    AUDIT_LOG_ID VARCHAR(36) NOT NULL,
    EMP_ID VARCHAR(36) NOT NULL,
    ACTION VARCHAR(50) NOT NULL,
    ENTITY_TYPE VARCHAR(50) NOT NULL,
    ENTITY_ID VARCHAR(36) NOT NULL,
    OLD_VALUE TEXT NULL,
    NEW_VALUE TEXT NULL,
    IP_ADDRESS VARCHAR(50) NULL,
    DEVICE_INFO TEXT NULL,
    CREATED_DATE DATETIME NOT NULL,
    CREATED_BY VARCHAR(36) NOT NULL,
    PRIMARY KEY (AUDIT_LOG_ID),
    CONSTRAINT FK_AL_EMP_ID FOREIGN KEY (EMP_ID) REFERENCES USER(EMP_ID)
);

CREATE INDEX IDX_AL_EMP_ID ON AUDIT_LOG(EMP_ID);
CREATE INDEX IDX_AL_ACTION ON AUDIT_LOG(ACTION);
CREATE INDEX IDX_AL_ENTITY ON AUDIT_LOG(ENTITY_TYPE, ENTITY_ID);
CREATE INDEX IDX_AL_CREATED_DATE ON AUDIT_LOG(CREATED_DATE);