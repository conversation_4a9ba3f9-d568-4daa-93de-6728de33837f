# Java Coding Guidelines Template

This document provides a framework for establishing Java coding standards, design patterns, and architectural guidelines for backend development. Use this template to create consistent coding standards across projects or to set up standards for new Java backend projects.

## Project Overview

[Provide a brief description of the project purpose and architecture]

## Tech Stack

### Backend

- **Framework**: [Primary backend framework (e.g., Spring Boot, Jakarta EE, Quarkus)]
- **Database**: [Database technologies (e.g., MySQL, PostgreSQL, MongoDB)]
- **Build Tool**: [Build tools (e.g., Maven, Gradle)]
- **Java Version**: [Java version]
- **API Style**: [API architectural style (e.g., REST, GraphQL, SOAP)]
- **Caching**: Redis
- **Libraries**:
  - Lombok: For reducing boilerplate code
  - [Key libraries (e.g., Jackson, Hibernate, JUnit)]
- **Logging**: [Logging framework (e.g., SLF4J, Log4j2)]
- **Monitoring**: [Monitoring tools (e.g., Prometheus, Micrometer)]

## Code Structure

### Backend Structure
```
src/
├── main/
│   ├── java/
│   │   ├── com/stpl/tech/<project_name>
│   │       ├── config/           # Application configuration
│   │       ├── controller/       # REST controllers
│   │       ├── dto/              # Data Transfer Objects
│   │       ├── entity/           # JPA entities
│   │       ├── exception/        # Custom exceptions
│   │       ├── mapper/           # DTO-Entity mappers
│   │       ├── repository/       # Data access layer
│   │       ├── service/          # Business logic
│   │           ├── impl/         # Service implementations
│   │       ├── util/             # Utility classes
│   │       └── Application.java  # Application entry point
│   └── resources/
│       ├── application.yml       # Application properties
│       └── logback.xml           # Logging configuration
└── test/
    ├── java/                     # Test classes
    └── resources/                # Test resources
└── db/
    ├── scripts/                     # Script Files
```

## Naming Conventions

### Classes

- **PascalCase** for class names: `UserService`, `OrderController`
- Follow clear suffix conventions:
  - `*Controller` for REST controllers
  - `*Service` and `*ServiceImpl` for services and their implementations
  - `*Repository` for data access
  - `*DTO` for data transfer objects
  - `*Entity` or domain objects without suffixes
  - `*Exception` for custom exceptions
  - `*Config` or `*Configuration` for configuration classes
  - `*Mapper` for mapping classes
  - `*Factory` for factories
  - `*Provider` for providers
  - `*Util` or `*Utils` for utility classes

### Methods

- **camelCase** for method names: `findByUsername()`, `processPayment()`
- Descriptive names that clearly indicate purpose
- Consistent verb prefixes:
  - `get*` for simple retrievals
  - `find*` for query operations that may return null
  - `create*` for creation operations
  - `update*` for update operations
  - `delete*` or `remove*` for deletion
  - `is*`, `has*`, `can*` for boolean methods
  - `process*` for operations with side effects

### Variables

- **camelCase** for variables and fields
- Meaningful names that fully convey purpose
- Avoid abbreviations unless widely understood
- Prefix boolean variables with `is`, `has`, or `should`
- Use plural names for collections

### Constants

- **UPPER_SNAKE_CASE** for constants: `MAX_RETRY_ATTEMPTS`, `API_BASE_URL`
- Declare constants as `static final`

### Packages

- All lowercase with dots as separators: `com.company.module.feature`
- Organized by feature and technical layer
- Avoid deep package hierarchies; aim for 2-4 levels

## Coding Style

### Code Organization

- Follow the [SOLID principles](https://en.wikipedia.org/wiki/SOLID)
- Single responsibility per class
- Clear separation of concerns
- Dependency injection instead of direct instantiation
- Immutable objects where possible
- Favor composition over inheritance

### Lombok Usage

- Use `@Getter` and `@Setter` for bean properties
- Use `@RequiredArgsConstructor` for dependency injection
- Use `@Builder` for complex object creation
- Use `@Value` for immutable classes
- Use `@Slf4j` for logging
- Use `@Data` sparingly (prefer explicit annotations)
- Avoid `@EqualsAndHashCode` on entity classes with relationships

### API Design

- Follow RESTful principles
- Use appropriate HTTP methods:
  - GET for retrieval
  - POST for creation
  - PUT for full updates
  - PATCH for partial updates
  - DELETE for deletion
- Consistent URL patterns:
  - Resource names in plural: `/users`, `/orders`
  - Resource identifiers in URLs: `/users/{id}`
  - Sub-resources with parent identifiers: `/users/{id}/addresses`
- Use request/response DTOs instead of entities
- Comprehensive exception handling with appropriate status codes
- API versioning strategy
- Consistent response formats

### Error Handling

- Create hierarchical exception structure
- Use custom exceptions for business rules
- Use standard exception types for standard cases
- Centralized exception handling with `@ControllerAdvice`
- Consistent error response format:
  ```json
  {
    "status": 400,
    "code": "VALIDATION_ERROR",
    "message": "Validation failed",
    "details": [
      {
        "field": "email",
        "message": "Must be valid email format"
      }
    ],
    "timestamp": "2023-05-05T12:34:56Z"
  }
  ```
- Avoid exposing sensitive internal details

### Logging

- Use SLF4J with Lombok's `@Slf4j`
- Log at appropriate levels:
  - ERROR: Errors that disrupt operation
  - WARN: Unusual but recoverable situations
  - INFO: Significant application events
  - DEBUG: Detailed information for troubleshooting
  - TRACE: Very detailed debugging
- Include context in log messages
- Avoid logging sensitive information
- Use structured logging where appropriate
- Add correlation IDs for request tracing

### Redis Caching

- Use Redis for:
  - Session management
  - Rate limiting
  - Distributed locks
  - Caching frequently accessed data
- Define clear cache keys with prefixes
- Set appropriate TTL (Time To Live) for cached items
- Implement cache invalidation strategies
- Use cache abstraction (e.g., Spring's `@Cacheable`)
- Common cache annotations:
  - `@Cacheable`: Cache method results
  - `@CachePut`: Update cache without affecting method execution
  - `@CacheEvict`: Remove entries from cache
  - `@Caching`: Combine multiple cache operations

## Design Patterns

### Core Patterns

1. **Dependency Injection**
   - Constructor injection preferred for required dependencies
   - Use Spring's `@Autowired` or constructor parameters
   - Enable component scanning with appropriate annotations

2. **Repository Pattern**
   - Use Spring Data repositories
   - Create repository interfaces extending Spring Data interfaces
   - Use derived query methods for simple queries
   - Use `@Query` annotation for complex queries
   - Custom repository implementations for complex operations

3. **Service Layer**
   - Business logic in service classes
   - Services should be stateless
   - Transaction management with `@Transactional`
   - Service interfaces and implementations
   - Chain of responsibility for complex workflows

4. **DTO Pattern**
   - Use Data Transfer Objects for API responses and requests
   - Keep DTOs immutable when possible
   - Use mapping libraries (e.g., MapStruct) for entity-DTO conversion
   - Nest DTOs for complex responses
   - Version DTOs when API evolves

5. **Factory Pattern**
   - Use factories for creating complex objects
   - Static factory methods for common construction patterns
   - Injectable factories for objects requiring dependencies

6. **Builder Pattern**
   - Use Lombok `@Builder` for complex object creation
   - Custom builders for complex construction logic
   - Director classes to orchestrate complex building processes

7. **Strategy Pattern**
   - Use interfaces for different algorithm implementations
   - Inject appropriate strategy based on conditions
   - Use enums with strategies for simple cases

8. **Template Method Pattern**
   - Abstract base classes with concrete implementations
   - Define algorithm skeleton in base class
   - Override specific steps in subclasses

9. **Decorator Pattern**
   - Use for adding responsibilities to objects dynamically
   - Spring AOP or hand-coded decorators
   - Wrap service methods for cross-cutting concerns

## Performance Considerations

### Database Optimization

- Use appropriate indexes
- Write efficient queries
- Use pagination for large result sets
- Batch operations for bulk data
- Use projections for partial data retrieval
- Optimize entity relationships (fetch types, cascade types)
- Consider using database-specific features

### Caching Strategy

- Multi-level caching:
  - L1: JPA/Hibernate cache
  - L2: Application cache
  - L3: Redis distributed cache
- Cache read-heavy data
- Cache invalidation triggers
- Cache warm-up strategies
- Appropriate cache TTLs
- Monitor cache hit ratios

### API Performance

- Pagination for collection endpoints
- Response filtering (GraphQL or custom)
- Compression for large responses
- Connection pooling
- Asynchronous processing for long-running operations
- Rate limiting for public APIs

### JVM Tuning

- Appropriate heap size configuration
- Garbage collection strategy
- JVM monitoring
- Thread pool sizing
- Connection pool sizing

## Security Practices

### Authentication and Authorization

- Secure authentication mechanism (OAuth2, JWT, etc.)
- Role-based or attribute-based access control
- Method-level security with Spring Security annotations
- Session management (stateless preferred)
- Secure password handling (hashing, salting)

### Data Protection

- Encrypt sensitive data at rest
- TLS for data in transit
- Secure headers configuration
- PII (Personally Identifiable Information) handling
- Data masking in logs

### Input Validation

- Validate all client inputs
- Use Bean Validation (JSR 380)
- Custom validators for complex rules
- Sanitize inputs to prevent injection attacks
- Input size limits

### API Security

- CSRF protection where appropriate
- CORS configuration
- Rate limiting
- API keys or OAuth tokens
- Security headers

## Testing Approach

### Unit Testing

- Test frameworks: JUnit 5, Mockito
- Test each unit in isolation
- Mock external dependencies
- Focus on behavior, not implementation
- Test edge cases and error conditions
- Target high test coverage (e.g., >80%)

### Integration Testing

- Test component interactions
- Database integration with test containers
- Test realistic scenarios
- Spring Boot Test for context loading
- Profile-specific test configurations

### API Testing

- Test controllers with MockMvc or RestAssured
- Verify request/response contracts
- Test all response status codes
- Test authentication and authorization
- API contract testing with Spring Cloud Contract

### Performance Testing

- Load testing with tools like JMeter or Gatling
- Benchmark critical operations
- Test caching effectiveness
- Monitor memory usage and leaks
- Database query performance

## Documentation Standards

### Code Documentation

- Javadoc for public APIs
- Class-level documentation explaining purpose
- Method-level documentation for non-trivial methods
- Document parameters, return values, and exceptions
- Document thread-safety considerations
- Document design decisions and trade-offs

### API Documentation

- Use OpenAPI/Swagger for REST APIs
- Document all endpoints, parameters, and responses
- Provide example requests and responses
- Document error codes and formats
- Keep documentation in sync with implementation

### Architecture Documentation

- High-level architecture diagrams
- Component interaction diagrams
- Data model documentation
- Deployment architecture
- Decision records for significant choices

## Version Control Practices

### Branching Strategy

- Main/master branch for stable code
- Feature branches for new features
- Release branches for release preparation
- Hotfix branches for critical fixes
- Use descriptive branch names: `feature/user-authentication`, `bugfix/order-calculation`

### Commit Standards

- Write meaningful commit messages
- Use present tense, imperative style: "Add user authentication" not "Added user authentication"
- Keep commits focused on a single change
- Reference issue numbers in commit messages
- Consider using Conventional Commits format

### Code Review Process

- Required reviews before merging
- Automated checks (CI) must pass
- Review checklist for common issues
- Focus on functionality, security, performance, and maintainability
- Constructive feedback

## Deployment and DevOps

### Environment Strategy

- Development, testing, staging, production environments
- Environment-specific configurations
- Feature flags for controlled rollouts
- Blue/green or canary deployments
- Configuration management with Spring profiles

### CI/CD

- Automated builds
- Unit and integration tests in pipeline
- Static code analysis
- Security scanning
- Automated deployment to test environments
- Manual approval for production deployment

### Monitoring and Metrics

- Application health endpoints
- Metrics collection with Micrometer
- Distributed tracing
- Log aggregation
- Alert configuration for critical failures
- Performance dashboards

## ORM BEST PRACTICES AND GUIDELINES:

### ORM PREFERENCE:
- Use Hibernate ORM for all database operations when possible
- Avoid raw SQL queries unless absolutely necessary for optimization
- Document any raw SQL usage with performance benchmarks justifying its use
- Implement proper type annotations for all ORM models

### MODEL DEFINITIONS:
- Create Hibernate models for all tables following the same naming conventions
- Use Hibernate Metadata for table definitions
- Implement relationships using Hibernate relationship() decorators
- Use backref/back_populates for bidirectional relationships
- Use hybrid properties for derived attributes
- Implement proper validation in models or schema classes
- Use Column definition instead of server_default for all fields

### QUERYING BEST PRACTICES:
- Use filter() over filter_by() for complex conditions
- Implement proper joins using join() method
- Use eager loading (joinedload, selectinload) to avoid N+1 query problems
- Apply database-level filtering before fetching data
- Use limit(), offset() for pagination
- Implement proper ordering with order_by()
- Use group_by() with having() for aggregations
- Avoid loading unnecessary data with query optimization
- Use aliased() for self-referential relationships
- Implement custom query classes for common operations

### DATA ACCESS PATTERNS:
- Implement Repository pattern for data access operations
- Use Unit of Work pattern for transaction management
- Create service layers to coordinate complex operations
- Implement proper exception handling for database errors
- Use database sessions wisely and always close them
- Implement connection pooling for production environments
- Use query pagination for large datasets
- Implement proper transactions with commit/rollback
- Use with statement for automatic session management

### AUDIT HISTORY AND METADATA:
- Implement proper auditing with these fields on metadata tables:
  - CREATED_DATE (datetime)
  - CREATED_BY (varchar)
  - UPDATED_DATE (datetime)
  - UPDATED_BY (varchar)
  - DELETED_DATE (datetime, nullable)
  - DELETED_BY (varchar, nullable)
- Use Hibernate event listeners to automatically update audit fields
- Implement soft delete pattern using DELETED_DATE
- Add STATUS field (VARCHAR) with values 'ACTIVE'/'INACTIVE' on metadata tables
- Implement proper archiving strategy for historical data

### PERFORMANCE CONSIDERATIONS:
- Use bulk operations for multiple inserts/updates
- Implement proper indexing on ORM model definitions
- Use query caching for frequently accessed data
- Monitor and log slow queries
- Use profiling tools to identify ORM bottlenecks
- Implement proper connection pool configuration
- Use pagination for large result sets
- Consider materialized views for complex aggregations
- Implement database partitioning for very large tables

## DATABASE DESIGN AND MANAGEMENT GUIDELINES:

### NAMING CONVENTIONS:
- All table names MUST be in UPPERCASE with UNDERSCORES separating words (e.g., USER_PROFILE)
- All column names MUST be in UPPERCASE with UNDERSCORES separating words (e.g., FIRST_NAME)
- Primary keys should be named as TABLE_NAME_ID (e.g., USER_PROFILE_ID)
- Foreign keys should be named as REFERENCED_TABLE_NAME_ID (e.g., DEPARTMENT_ID)
- Indexes should be named as IDX_TABLE_NAME_COLUMN_NAME (e.g., IDX_USER_PROFILE_EMAIL)
- Unique constraints should be named as UQ_TABLE_NAME_COLUMN(S) (e.g., UQ_USER_PROFILE_EMAIL)

### SQL FILE MANAGEMENT:
- All SQL scripts MUST be stored in a 'setup/sql' directory
- SQL file naming pattern: V{version_number}__{description}.sql (e.g., V1.0.1__create_user_tables.sql)
- Version numbers should follow semantic versioning (MAJOR.MINOR.PATCH)
- Each file should contain exactly one schema change (e.g., one table creation)
- Add comments to each SQL file describing the purpose of the change
- Include rollback scripts for each change when possible
- All SQL files must be under version control
- Order files numerically to ensure proper execution sequence

### INDEXING STRATEGY:
- CREATE INDEXES on all columns used in:
  - WHERE clauses
  - JOIN conditions
  - ORDER BY clauses
  - GROUP BY operations
- Consider covering indexes for frequently queried columns
- Monitor index usage and remove unused indexes
- Create composite indexes for multi-column filters
- Avoid over-indexing as it impacts write performance
- Consider partial indexes for frequently filtered subsets

### OPTIMIZATION BEST PRACTICES:
- Always use prepared statements to prevent SQL injection
- Filter data at the database level, NOT in application code
- Use EXPLAIN/EXPLAIN ANALYZE to verify query efficiency
- Avoid SELECT * and only request needed columns
- Use appropriate data types for columns
- Consider partitioning for very large tables
- Implement connection pooling in application code
- Set appropriate isolation levels for transactions
- Use batch operations for bulk inserts/updates
- Implement query timeouts to prevent long-running queries

### RELATIONSHIPS AND CONSTRAINTS:
- Always define PRIMARY KEY constraints
- Create FOREIGN KEY constraints for all relationships
- Use ON DELETE/UPDATE actions appropriate for business rules
- Implement CHECK constraints for data validation
- Add UNIQUE constraints where appropriate
- Consider using database triggers for complex integrity rules
- Document all relationships in schema documentation

### COLUMN DEFAULTS AND TIMESTAMPS:
- DO NOT set database-level DEFAULT values for timestamp columns
- Handle timestamp creation/updates in application code
- Set explicit NOT NULL constraints when appropriate
- Avoid using database sequences/auto-increment for IDs in distributed systems
- Consider using UUIDs for distributed systems
- Document all column constraints in schema documentation
- All Table and documents name should be in Upper case

### Schema Evolution

- Use migration tools (Flyway, Liquibase)
- Version database changes
- Backward compatible changes when possible
- Data migration strategies
- Testing database migrations

### Data Management

- Backup and restore procedures
- Data archiving strategy
- Data retention policies
- Master data management
- Reference data handling

## Conclusion

These coding guidelines aim to ensure consistency, maintainability, and quality across Java backend codebases. All contributors should adhere to these standards when generating or modifying code for the project.

