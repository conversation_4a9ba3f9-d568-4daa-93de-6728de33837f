# Face Registration Approval Flow Documentation

This document outlines the complete database flow for face registration approval process, including both internal database and Flowable database interactions.

## Database Structure

### Internal Database Tables
1. `APPROVAL`: Main approval request table
2. `APPROVAL_STEP`: Individual approval steps
3. `BIOMETRIC_TEMPLATE`: Face registration data
4. `NOTIFICATION`: User notifications
5. `AUDIT_LOG`: Audit trail

### Flowable Database Tables

#### Runtime Tables (ACT_RU_*)
1. `ACT_RU_EXECUTION`: Process instance tracking
   - Stores active process instances
   - Tracks execution paths
   - Maintains process hierarchy

2. `ACT_RU_TASK`: Current active tasks
   - Stores active user tasks
   - Contains task assignments
   - Tracks task status

3. `ACT_RU_VARIABLE`: Process variables
   - Stores process instance variables
   - Contains task variables
   - Maintains variable scope

4. `ACT_RU_IDENTITYLINK`: Identity links
   - Stores task assignments
   - Contains candidate users/groups
   - Tracks task delegation

5. `ACT_RU_EVENT_SUBSCR`: Event subscriptions
   - Stores event listeners
   - Contains signal event subscriptions
   - Tracks message event subscriptions

6. `ACT_RU_JOB`: Job execution
   - Stores async jobs
   - Contains timer jobs
   - Tracks message jobs

7. `ACT_RU_DELEGATION`: Task delegation
   - Stores task delegation history
   - Contains delegation reasons
   - Tracks delegation dates

#### History Tables (ACT_HI_*)
1. `ACT_HI_TASKINST`: Task instance history
   - Stores completed tasks
   - Contains task duration
   - Tracks task assignments

2. `ACT_HI_PROCINST`: Process instance history
   - Stores completed processes
   - Contains process duration
   - Tracks process variables

3. `ACT_HI_ACTINST`: Activity instance history
   - Stores activity execution history
   - Contains activity duration
   - Tracks activity transitions

4. `ACT_HI_VARINST`: Variable instance history
   - Stores variable value history
   - Contains variable scope
   - Tracks variable updates

5. `ACT_HI_IDENTITYLINK`: Identity link history
   - Stores historical task assignments
   - Contains candidate user/group history
   - Tracks delegation history

6. `ACT_HI_COMMENT`: Comments and attachments
   - Stores task comments
   - Contains process comments
   - Tracks comment types

7. `ACT_HI_ATTACHMENT`: Attachments
   - Stores task attachments
   - Contains process attachments
   - Tracks attachment metadata

## Complete Flow

### 1. Initial Face Registration Submission

#### Internal DB:
```sql
-- Create approval request
INSERT INTO APPROVAL (
    APPROVAL_ID,
    ENTITY_TYPE,
    ENTITY_ID,
    APPROVAL_STATUS,
    APPROVAL_STATUS,
    CREATED_DATE,
    CREATED_BY,
    UPDATED_DATE,
    UPDATED_BY
) VALUES (
    UUID(),
    'BIOMETRIC_TEMPLATE',
    [BIOMETRIC_TEMPLATE_ID],
    'PENDING',
    'ACTIVE',
    NOW(),
    'SYSTEM',
    NOW(),
    'SYSTEM'
);

-- Create approval steps
INSERT INTO APPROVAL_STEP (
    APPROVAL_STEP_ID,
    APPROVAL_ID,
    STEP_NUMBER,
    APPROVER_ID,
    STEP_STATUS,
    APPROVAL_STEP_STATUS,
    CREATED_DATE,
    CREATED_BY,
    UPDATED_DATE,
    UPDATED_BY
) VALUES 
-- Step 1: First Level Approver
(UUID(), [APPROVAL_ID], 1, [FIRST_APPROVER_ID], 'PENDING', 'ACTIVE', NOW(), 'SYSTEM', NOW(), 'SYSTEM'),
-- Step 2: Second Level Approver
(UUID(), [APPROVAL_ID], 2, [SECOND_APPROVER_ID], 'PENDING', 'ACTIVE', NOW(), 'SYSTEM', NOW(), 'SYSTEM'),
-- Step 3: Final Approver
(UUID(), [APPROVAL_ID], 3, [FINAL_APPROVER_ID], 'PENDING', 'ACTIVE', NOW(), 'SYSTEM', NOW(), 'SYSTEM');
```

#### Flowable DB:
```sql
-- Process instance created
-- ACT_RU_EXECUTION:
-- PROC_INST_ID_: [GENERATED_ID]
-- BUSINESS_KEY_: [BIOMETRIC_TEMPLATE_ID]
-- ACT_ID_: 'startEvent'

-- Process variables
-- ACT_RU_VARIABLE:
-- EXECUTION_ID_: [PROC_INST_ID]
-- NAME_: 'approvalId'
-- VALUE_: [APPROVAL_ID]

-- First task created
-- ACT_RU_TASK:
-- PROC_INST_ID_: [PROC_INST_ID]
-- TASK_DEF_KEY_: 'firstLevelApproval'
-- ASSIGNEE_: [FIRST_APPROVER_ID]
```

### 2. First Level Approval

#### Flowable DB:
```sql
-- Update current task
UPDATE ACT_RU_TASK 
SET ASSIGNEE_ = NULL 
WHERE TASK_DEF_KEY_ = 'firstLevelApproval';

-- Create next task
INSERT INTO ACT_RU_TASK (
    ID_,
    PROC_INST_ID_,
    TASK_DEF_KEY_,
    ASSIGNEE_,
    NAME_,
    CREATE_TIME_
) VALUES (
    UUID(),
    [PROC_INST_ID],
    'secondLevelApproval',
    [SECOND_APPROVER_ID],
    'Second Level Approval',
    NOW()
);
```

#### Internal DB:
```sql
-- Update approval step
UPDATE APPROVAL_STEP 
SET STEP_STATUS = 'APPROVED',
    COMMENTS = 'Approved by First Level Approver',
    UPDATED_DATE = NOW(),
    UPDATED_BY = [FIRST_APPROVER_ID]
WHERE APPROVAL_ID = [APPROVAL_ID]
AND STEP_NUMBER = 1;

-- Create notification
INSERT INTO NOTIFICATION (
    NOTIFICATION_ID,
    EMP_ID,
    NOTIFICATION_TYPE,
    TITLE,
    MESSAGE,
    ENTITY_TYPE,
    ENTITY_ID,
    IS_READ,
    NOTIFICATION_STATUS,
    CREATED_DATE,
    CREATED_BY,
    UPDATED_DATE,
    UPDATED_BY
) VALUES (
    UUID(),
    [SECOND_APPROVER_ID],
    'APPROVAL_REQUEST',
    'New Face Registration Approval Request',
    'Please review the face registration request',
    'BIOMETRIC_TEMPLATE',
    [BIOMETRIC_TEMPLATE_ID],
    FALSE,
    'ACTIVE',
    NOW(),
    'SYSTEM',
    NOW(),
    'SYSTEM'
);
```

### 3. Second Level Approval

#### Flowable DB:
```sql
-- Update current task
UPDATE ACT_RU_TASK 
SET ASSIGNEE_ = NULL 
WHERE TASK_DEF_KEY_ = 'secondLevelApproval';

-- Create next task
INSERT INTO ACT_RU_TASK (
    ID_,
    PROC_INST_ID_,
    TASK_DEF_KEY_,
    ASSIGNEE_,
    NAME_,
    CREATE_TIME_
) VALUES (
    UUID(),
    [PROC_INST_ID],
    'finalApproval',
    [FINAL_APPROVER_ID],
    'Final Approval',
    NOW()
);
```

#### Internal DB:
```sql
-- Update approval step
UPDATE APPROVAL_STEP 
SET STEP_STATUS = 'APPROVED',
    COMMENTS = 'Approved by Second Level Approver',
    UPDATED_DATE = NOW(),
    UPDATED_BY = [SECOND_APPROVER_ID]
WHERE APPROVAL_ID = [APPROVAL_ID]
AND STEP_NUMBER = 2;

-- Create notification
INSERT INTO NOTIFICATION (
    NOTIFICATION_ID,
    EMP_ID,
    NOTIFICATION_TYPE,
    TITLE,
    MESSAGE,
    ENTITY_TYPE,
    ENTITY_ID,
    IS_READ,
    NOTIFICATION_STATUS,
    CREATED_DATE,
    CREATED_BY,
    UPDATED_DATE,
    UPDATED_BY
) VALUES (
    UUID(),
    [FINAL_APPROVER_ID],
    'APPROVAL_REQUEST',
    'New Face Registration Approval Request',
    'Please review the face registration request',
    'BIOMETRIC_TEMPLATE',
    [BIOMETRIC_TEMPLATE_ID],
    FALSE,
    'ACTIVE',
    NOW(),
    'SYSTEM',
    NOW(),
    'SYSTEM'
);
```

### 4. Final Approval

#### Flowable DB:
```sql
-- Update current task
UPDATE ACT_RU_TASK 
SET ASSIGNEE_ = NULL 
WHERE TASK_DEF_KEY_ = 'finalApproval';

-- Process completes
UPDATE ACT_RU_EXECUTION 
SET ACT_ID_ = 'endEvent' 
WHERE PROC_INST_ID_ = [PROC_INST_ID];
```

#### Internal DB:
```sql
-- Update final step
UPDATE APPROVAL_STEP 
SET STEP_STATUS = 'APPROVED',
    COMMENTS = 'Final approval completed',
    UPDATED_DATE = NOW(),
    UPDATED_BY = [FINAL_APPROVER_ID]
WHERE APPROVAL_ID = [APPROVAL_ID]
AND STEP_NUMBER = 3;

-- Update main approval
UPDATE APPROVAL 
SET APPROVAL_STATUS = 'APPROVED',
    UPDATED_DATE = NOW(),
    UPDATED_BY = [FINAL_APPROVER_ID]
WHERE APPROVAL_ID = [APPROVAL_ID];

-- Update biometric template
UPDATE BIOMETRIC_TEMPLATE 
SET APPROVAL_STATUS = 'APPROVED',
    APPROVER_ID = [FINAL_APPROVER_ID],
    APPROVAL_DATE = NOW(),
    UPDATED_DATE = NOW(),
    UPDATED_BY = [FINAL_APPROVER_ID]
WHERE BIOMETRIC_TEMPLATE_ID = [BIOMETRIC_TEMPLATE_ID];
```

### 5. Rejection Flow (at any step)

#### Flowable DB:
```sql
-- Update current task
UPDATE ACT_RU_TASK 
SET ASSIGNEE_ = NULL 
WHERE TASK_DEF_KEY_ = [CURRENT_TASK_KEY];

-- Process completes with rejection
UPDATE ACT_RU_EXECUTION 
SET ACT_ID_ = 'rejectEvent' 
WHERE PROC_INST_ID_ = [PROC_INST_ID];
```

#### Internal DB:
```sql
-- Update current step
UPDATE APPROVAL_STEP 
SET STEP_STATUS = 'REJECTED',
    COMMENTS = 'Rejection reason here',
    UPDATED_DATE = NOW(),
    UPDATED_BY = [APPROVER_ID]
WHERE APPROVAL_ID = [APPROVAL_ID]
AND STEP_NUMBER = [CURRENT_STEP];

-- Update main approval
UPDATE APPROVAL 
SET APPROVAL_STATUS = 'REJECTED',
    UPDATED_DATE = NOW(),
    UPDATED_BY = [APPROVER_ID]
WHERE APPROVAL_ID = [APPROVAL_ID];

-- Update biometric template
UPDATE BIOMETRIC_TEMPLATE 
SET APPROVAL_STATUS = 'REJECTED',
    APPROVER_ID = [APPROVER_ID],
    REJECTION_REASON = 'Rejection reason here',
    UPDATED_DATE = NOW(),
    UPDATED_BY = [APPROVER_ID]
WHERE BIOMETRIC_TEMPLATE_ID = [BIOMETRIC_TEMPLATE_ID];
```

## Status Queries

### Get Current Task from Flowable
```sql
SELECT 
    t.*,
    v.VALUE_ as APPROVAL_ID
FROM ACT_RU_TASK t
JOIN ACT_RU_VARIABLE v ON t.PROC_INST_ID_ = v.EXECUTION_ID_
WHERE v.NAME_ = 'approvalId'
AND v.VALUE_ = [APPROVAL_ID];
```

### Get Process Variables
```sql
SELECT 
    v.NAME_,
    v.VALUE_
FROM ACT_RU_VARIABLE v
WHERE v.EXECUTION_ID_ = [PROC_INST_ID];
```

### Get Approval Status from Internal DB
```sql
SELECT 
    a.APPROVAL_STATUS,
    s.STEP_NUMBER,
    s.STEP_STATUS,
    s.APPROVER_ID,
    u.USER_NAME as APPROVER_NAME
FROM APPROVAL a
JOIN APPROVAL_STEP s ON a.APPROVAL_ID = s.APPROVAL_ID
JOIN USER u ON s.APPROVER_ID = u.EMP_ID
WHERE a.APPROVAL_ID = [APPROVAL_ID]
ORDER BY s.STEP_NUMBER;
```

## Key Points

1. The flow maintains synchronization between:
   - Internal database (business data)
   - Flowable database (process flow)
   - Notifications (user communication)

2. Each approval step involves:
   - Updating Flowable task status
   - Creating next task in Flowable
   - Updating internal approval step
   - Sending notification to next approver

3. The process can be tracked through:
   - Flowable process instance
   - Approval request ID
   - Biometric template ID

4. Audit trail is maintained through:
   - Approval step updates
   - Process history in Flowable
   - Audit log entries

5. Approver Assignment:
   - Approvers are dynamically assigned based on roles and context
   - Each step can have multiple possible approvers
   - Approver assignment can be based on:
     - Unit/Department hierarchy
     - Role-based access
     - Business rules
     - Geographic location 