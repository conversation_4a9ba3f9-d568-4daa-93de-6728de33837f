# Attendance Service API Documentation

## Standard Response Format
All APIs follow a standard response format:

```json
{
    "success": boolean,
    "data": object | array | null,
    "error": {
        "code": string,
        "message": string,
        "details": object | null
    },
    "pagination": {
        "pageNumber": number,
        "pageSize": number,
        "totalElements": number,
        "totalPages": number
    }
}
```

## HTTP Status Codes
- `200 OK`: Request succeeded
- `201 Created`: Resource created successfully
- `400 Bad Request`: Invalid request parameters
- `401 Unauthorized`: Authentication failed
- `403 Forbidden`: Authorization failed
- `404 Not Found`: Resource not found
- `409 Conflict`: Business validation failed
- `422 Unprocessable Entity`: Required fields missing
- `429 Too Many Requests`: Rate limit exceeded
- `500 Internal Server Error`: System error

## Error Codes
- `AUTH_001` (401): Authentication failed
- `AUTH_002` (403): Authorization failed
- `VAL_001` (400): Invalid request parameters
- `VAL_002` (422): Required fields missing
- `BIZ_001` (409): Business validation failed
- `SYS_001` (500): System error

## 1. Get Pending Approval Requests
Get all pending approval requests for an employee.

**Endpoint:** `GET /api/v1/approvals/pending/{employeeId}`

**Response:**
```json
{
    "success": true,
    "data": [
        {
            "requestId": "number",
            "employeeId": "number",
            "employeeName": "string",
            "employeeDesignation": "string",
            "requestType": "string",
            "status": "string",
            "reason": "string",
            "requestDate": "datetime",
            "lastUpdatedDate": "datetime",
            "approverName": "string",
            "approverDesignation": "string",
            "remarks": "string",
            "metadata": "string"
        }
    ],
    "error": null,
    "pagination": null
}
```

**Error Response:**
```json
{
    "success": false,
    "data": null,
    "error": {
        "code": "AUTH_001",
        "message": "Authentication failed",
        "details": null
    },
    "pagination": null
}
```

## 2. Get Completed Approvals
Get completed approval requests with pagination.

**Endpoint:** `GET /api/v1/approvals/completed/{employeeId}`

**Query Parameters:**
- `pageNumber` (optional, default: 0): Page number (0-based)
- `pageSize` (optional, default: 10): Number of items per page

**Response:**
```json
{
    "success": true,
    "data": [
        {
            "requestId": "number",
            "employeeId": "number",
            "employeeName": "string",
            "employeeDesignation": "string",
            "requestType": "string",
            "status": "string",
            "reason": "string",
            "requestDate": "datetime",
            "lastUpdatedDate": "datetime",
            "approverName": "string",
            "approverDesignation": "string",
            "remarks": "string",
            "metadata": "string"
        }
    ],
    "error": null,
    "pagination": {
        "pageNumber": 0,
        "pageSize": 10,
        "totalElements": 100,
        "totalPages": 10
    }
}
```

## 3. Get Attendance History
Get attendance history for an employee.

**Endpoint:** `GET /api/v1/attendance/{employeeId}`

**Query Parameters:**
- `date` (optional): Specific date (YYYY-MM-DD)
- `month` (optional): Month number (1-12)
- `year` (optional): Year

**Parameter Rules:**
1. If `date` is provided, it will return attendance for that specific date
2. If `month` and `year` are provided together, it will return attendance for that month
3. If only `year` is provided, it will return attendance for the entire year
4. At least one of these combinations must be provided:
   - `date` alone
   - `month` and `year` together
   - `year` alone

**Error Response for Invalid Parameters:**
```json
{
    "success": false,
    "data": null,
    "error": {
        "code": "VAL_001",
        "message": "Invalid request parameters",
        "details": {
            "message": "Either date, or month+year, or year must be provided"
        }
    },
    "pagination": null
}
```

**Example Requests:**
1. Get attendance for a specific date:
   ```
   GET /api/v1/attendance/123?date=2024-03-20
   ```

2. Get attendance for a specific month:
   ```
   GET /api/v1/attendance/123?month=3&year=2024
   ```

3. Get attendance for a specific year:
   ```
   GET /api/v1/attendance/123?year=2024
   ```

**Response:**
```json
{
    "success": true,
    "data": [
        {
            "employeeId": "number",
            "employeeName": "string",
            "designation": "string",
            "checkInTime": "datetime",
            "checkOutTime": "datetime",
            "checkInImageUrl": "string",
            "checkOutImageUrl": "string",
            "status": "string",
            "remarks": "string"
        }
    ],
    "error": null,
    "pagination": null
}
```

## 4. Get Current Day Attendance
Get today's attendance status for an employee.

**Endpoint:** `GET /api/v1/attendance/today/{employeeId}`

**Response:**
```json
{
    "success": true,
    "data": {
        "employeeId": "number",
        "employeeName": "string",
        "designation": "string",
        "checkInTime": "datetime",
        "checkOutTime": "datetime",
        "checkInImageUrl": "string",
        "checkOutImageUrl": "string",
        "status": "string",
        "remarks": "string"
    },
    "error": null,
    "pagination": null
}
```

## 5. Approve/Reject Request
Approve or reject an approval request.

**Endpoint:** `POST /api/v1/approvals/{requestId}/action`

**Request Body:**
```json
{
    "action": "string", // "APPROVE" or "REJECT"
    "remarks": "string",
    "metadata": "object" // Optional additional data
}
```

**Response:**
```json
{
    "success": true,
    "data": {
        "requestId": "number",
        "status": "string",
        "action": "string",
        "actionDate": "datetime",
        "remarks": "string"
    },
    "error": null,
    "pagination": null
}
```

## 6. Get Approval History
Get approval history for a specific request.

**Endpoint:** `GET /api/v1/approvals/{requestId}/history`

**Response:**
```json
{
    "success": true,
    "data": [
        {
            "requestId": "number",
            "action": "string",
            "actionDate": "datetime",
            "actionBy": "string",
            "actionByDesignation": "string",
            "remarks": "string",
            "metadata": "string"
        }
    ],
    "error": null,
    "pagination": null
}
```

## Common Error Responses

### Authentication Error
```json
{
    "success": false,
    "data": null,
    "error": {
        "code": "AUTH_001",
        "message": "Authentication failed",
        "details": null
    },
    "pagination": null
}
```
Status Code: 401 Unauthorized

### Validation Error
```json
{
    "success": false,
    "data": null,
    "error": {
        "code": "VAL_001",
        "message": "Invalid request parameters",
        "details": {
            "field": "action",
            "message": "Action must be either APPROVE or REJECT"
        }
    },
    "pagination": null
}
```
Status Code: 400 Bad Request

### Business Error
```json
{
    "success": false,
    "data": null,
    "error": {
        "code": "BIZ_001",
        "message": "Request already processed",
        "details": {
            "currentStatus": "APPROVED",
            "processedDate": "2024-03-20T10:30:00"
        }
    },
    "pagination": null
}
```
Status Code: 409 Conflict

### System Error
```json
{
    "success": false,
    "data": null,
    "error": {
        "code": "SYS_001",
        "message": "Internal server error",
        "details": null
    },
    "pagination": null
}
```
Status Code: 500 Internal Server Error

## Notes for Frontend Implementation
1. All dates are in ISO 8601 format
2. All APIs require authentication token in header: `Authorization: Bearer <token>`
3. Pagination is 0-based (first page is 0)
4. Image URLs are CloudFront URLs
5. Status values are standardized:
   - For attendance: "PRESENT", "ABSENT", "MIDDAY"
   - For approvals: "PENDING", "APPROVED", "REJECTED"
   - For actions: "APPROVE", "REJECT"

## Rate Limiting
- Maximum 100 requests per minute per IP
- Maximum 1000 requests per hour per user

## Versioning
- Current API version: v1
- Version is included in the URL path
- Breaking changes will be released as new versions

## Support
For any API-related issues or questions, please contact:
- Email: <EMAIL>
- Internal Support Portal: https://support.stpl.com 