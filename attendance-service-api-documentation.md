# Attendance Service API Documentation

## 1. Attendance Management APIs

### Mark Attendance Punch
```http
POST /api/v1/attendance/punch
```

**Description:** Records an attendance punch (check-in/mid-day/check-out) for an employee.

**Request Body:**
```json
{
  "employeeId": "integer",     // Optional: Employee's ID (if not provided, biometricId is required)
  "base64Image": "string",     // Optional: Base64 encoded image (required if employeeId not provided)
  "unitId": "integer",         // Required: Unit identifier
  "punchTime": "datetime",     // Optional: ISO-8601 timestamp (defaults to current time)
  "macAddress": "string",      // Optional: Device MAC address
  "geoLocation": "string",     // Optional: Location coordinates
  "createdBy": "string",       // Optional: SYSTEM or USER
  "remarks": "string",         // Optional: Additional remarks
  "isSpecialCase": "boolean",  // Optional: For training, off-site work etc.
  "specialCaseType": "string", // Optional: TRAINING, OFF_SITE, etc.
  "biometricId": "string",     // Optional: For biometric authentication
  "punchType": "string",       // Optional: CHECK_IN, MID_DAY, CHECK_OUT
  "saveFailedImage": "boolean", // Optional: Whether to save failed biometric images to S3 (default: false)
  "biometricDeviceId": "string" // Optional: Device ID in format terminalId_unitType_unitId
}
```

**Example Request:**
```json
{
  "employeeId": 123,
  "base64Image": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...",
  "unitId": 1,
  "punchTime": "2024-03-20T09:00:00Z",
  "geoLocation": "19.0760,72.8777",
  "createdBy": "SYSTEM",
  "biometricDeviceId": "TERM001_OFFICE_1"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "success": true,
    "message": "Check-in recorded successfully",
    "punchType": "CHECK_IN",
    "punchTime": "2024-03-20T09:00:00Z",
    "employeeCode": "EMP001",
    "employeeName": "John Doe",
    "createdBy": "SYSTEM",
    "systemGeneratedEntries": null,
    "nextAction": "SUGGESTED_MIDDAY",
    "imageUrl": "https://cloudfront.net/attendance/EMP001/1234567890.jpg",
    "registeredImageUrl": "https://cloudfront.net/employees/EMP001.jpg"
  }
}
```

**Error Cases:**
1. Invalid Employee
```json
{
  "success": false,
  "data": {
    "message": "Invalid employee ID",
    "code": "VALIDATION_ERROR"
  }
}
```

2. Not Eligible for Unit
```json
{
  "success": false,
  "data": {
    "message": "Employee not eligible for this unit",
    "code": "NOT_ELIGIBLE"
  }
}
```

3. Biometric Verification Failed
```json
{
  "success": false,
  "data": {
    "message": "Biometric verification failed",
    "code": "BIOMETRIC_VERIFICATION_FAILED"
  }
}
```

4. Duplicate Punch
```json
{
  "success": true,
  "data": {
    "success": false,
    "message": "Punch ignored - within duplicate threshold",
    "punchType": "CHECK_IN",
    "punchTime": "2024-03-20T09:00:00Z",
    "employeeCode": "EMP001",
    "employeeName": "John Doe"
  }
}
```

5. Max Punches Exceeded
```json
{
  "success": true,
  "data": {
    "success": false,
    "message": "Maximum punches allowed per day exceeded",
    "punchType": "CHECK_IN",
    "punchTime": "2024-03-20T09:00:00Z",
    "employeeCode": "EMP001",
    "employeeName": "John Doe"
  }
}
```

### Get Current Day Attendance
```http
GET /api/v1/attendance/today/{employeeId}
```

**Description:** Retrieves attendance records for the current day for a specific employee.

**Path Parameters:**
- `employeeId`: Required - Employee's ID (Long)

**Response:**
```json
{
  "success": true,
  "data": {
    "employeeId": 123,
    "employeeCode": "EMP001",
    "employeeName": "John Doe",
    "designation": "Software Engineer",
    "attendanceType": "CHECK_IN",
    "attendanceTime": "2024-03-20T09:00:00Z",
    "status": "PRESENT",
    "checkInTime": "2024-03-20T09:00:00Z",
    "checkOutTime": "2024-03-20T18:00:00Z",
    "checkInImageUrl": "https://cloudfront.net/attendance/EMP001/checkin.jpg",
    "checkOutImageUrl": "https://cloudfront.net/attendance/EMP001/checkout.jpg",
    "remarks": "No attendance record found for today"
  }
}
```

### Get Attendance History
```http
GET /api/v1/attendance/{employeeId}
```

**Description:** Retrieves attendance history for an employee with optional date/month/year filters.

**Path Parameters:**
- `employeeId`: Required - Employee's ID (Long)

**Query Parameters:**
- `date`: Optional - Specific date (YYYY-MM-DD)
- `month`: Optional - Month number (1-12)
- `year`: Optional - Year (YYYY)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "employeeId": 123,
      "employeeCode": "EMP001",
      "employeeName": "John Doe",
      "designation": "Software Engineer",
      "attendanceType": "CHECK_IN",
      "attendanceTime": "2024-03-20T09:00:00Z",
      "status": "PRESENT",
      "checkInTime": "2024-03-20T09:00:00Z",
      "checkOutTime": "2024-03-20T18:00:00Z",
      "checkInImageUrl": "https://cloudfront.net/attendance/EMP001/checkin.jpg",
      "checkOutImageUrl": "https://cloudfront.net/attendance/EMP001/checkout.jpg",
      "remarks": null
    }
  ]
}
```

**Error Case:**
```json
{
  "success": false,
  "data": {
    "message": "Either date or month/year or year must be provided",
    "code": "VALIDATION_ERROR"
  }
}
```

## 2. External Sync Management APIs

### Get Sync Status
```http
GET /api/v1/attendance/sync/status/{attendanceRecordId}
```

**Description:** Get the sync status for a specific attendance record.

**Path Parameters:**
- `attendanceRecordId`: Required - Attendance record ID (Long)

**Response:**
```json
{
  "id": 1,
  "employeeId": 123,
  "attendanceRecordId": 456,
  "externalSystemId": null,
  "syncStatus": "SUCCESS",
  "lastSyncedSrno": 456,
  "syncAttempts": 1,
  "lastSyncAttempt": "2024-03-20T10:30:00Z",
  "errorMessage": null,
  "externalResponse": "{\"status\":\"success\"}",
  "createdAt": "2024-03-20T09:00:00Z",
  "updatedAt": "2024-03-20T10:30:00Z"
}
```

### Get Sync Statistics
```http
GET /api/v1/attendance/sync/statistics
```

**Description:** Get overall sync statistics.

**Response:**
```json
{
  "totalRecords": 1000,
  "pendingRecords": 50,
  "successfulRecords": 900,
  "failedRecords": 30,
  "inProgressRecords": 20,
  "successRate": 90.0,
  "lastSyncTime": 1710921600000
}
```

### Retry Failed Syncs
```http
POST /api/v1/attendance/sync/retry-failed
```

**Description:** Retry all failed sync records.

**Response:**
```json
"Retry process initiated"
```

### Trigger Manual Sync
```http
POST /api/v1/attendance/sync/trigger-sync
```

**Description:** Manually trigger the sync process.

**Response:**
```json
"Sync process initiated"
```

### Resync Attendance Data
```http
POST /api/v1/attendance/sync/resync?fromDate=2024-01-01&toDate=2024-01-31&employeeId=123
```

**Description:** Resync attendance data for a specific date range and employee.

**Query Parameters:**
- `fromDate`: Required - Start date (YYYY-MM-DD)
- `toDate`: Required - End date (YYYY-MM-DD)
- `employeeId`: Required - Employee ID

**Response:**
```json
"Resync process initiated for employee: 123"
```

### Resync Failed Records
```http
POST /api/v1/attendance/sync/resync-failed
```

**Description:** Resync all failed records.

**Response:**
```json
"Failed records resync process initiated"
```

### Get Sync Records by Date Range
```http
GET /api/v1/attendance/sync/records?fromDate=2024-01-01&toDate=2024-01-31&employeeId=123
```

**Description:** Get sync records for a specific date range and employee.

**Query Parameters:**
- `fromDate`: Required - Start date (YYYY-MM-DD)
- `toDate`: Required - End date (YYYY-MM-DD)
- `employeeId`: Optional - Employee ID

**Response:**
```json
[
  {
    "id": 1,
    "employeeId": 123,
    "attendanceRecordId": 456,
    "syncStatus": "SUCCESS",
    "syncAttempts": 1,
    "lastSyncAttempt": "2024-01-15T10:30:00Z",
    "createdAt": "2024-01-15T09:00:00Z"
  }
]
```

### Get Failed Sync Records
```http
GET /api/v1/attendance/sync/failed-records
```

**Description:** Get all failed sync records.

**Response:**
```json
[
  {
    "id": 2,
    "employeeId": 124,
    "attendanceRecordId": 457,
    "syncStatus": "FAILED",
    "syncAttempts": 3,
    "lastSyncAttempt": "2024-01-15T11:00:00Z",
    "errorMessage": "Connection timeout",
    "createdAt": "2024-01-15T09:30:00Z"
  }
]
```

### Get Sync Summary
```http
GET /api/v1/attendance/sync/summary?fromDate=2024-01-01&toDate=2024-01-31&employeeId=123
```

**Description:** Get sync summary for a date range.

**Query Parameters:**
- `fromDate`: Required - Start date (YYYY-MM-DD)
- `toDate`: Required - End date (YYYY-MM-DD)
- `employeeId`: Optional - Employee ID

**Response:**
```json
{
  "totalRecords": 100,
  "successfulRecords": 90,
  "failedRecords": 5,
  "pendingRecords": 3,
  "inProgressRecords": 2,
  "successRate": 90.0,
  "fromDate": "2024-01-01",
  "toDate": "2024-01-31",
  "employeeId": 123
}
```

## 3. Metadata Management APIs

### Get Employee Attendance Eligible Units
```http
GET /api/v1/metadata/employees/{empId}/attendance-eligible-units
```

**Description:** Retrieves all units for which an employee has attendance eligibility.

**Path Parameters:**
- `empId`: Required - Employee's ID (String)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "unitId": 1,
      "unitName": "Head Office",
      "unitCode": "HO001",
      "startDate": "2024-01-01",
      "endDate": "2024-12-31",
      "status": "ACTIVE"
    },
    {
      "unitId": 2,
      "unitName": "Branch Office",
      "unitCode": "BO001",
      "startDate": "2024-03-01",
      "endDate": null,
      "status": "ACTIVE"
    }
  ]
}
```

**Response Fields:**
- `unitId`: Integer - Unique identifier for the unit
- `unitName`: String - Name of the unit
- `unitCode`: String - Code/identifier for the unit
- `startDate`: Date (YYYY-MM-DD) - Start date of eligibility (null if no start date)
- `endDate`: Date (YYYY-MM-DD) - End date of eligibility (null if no end date)
- `status`: String - Status of the eligibility mapping (ACTIVE, INACTIVE, etc.)

**Error Case:**
```json
{
  "success": false,
  "data": {
    "message": "Employee not found",
    "code": "EMPLOYEE_NOT_FOUND"
  }
}
```

### Get Employee Details
```http
GET /api/v1/metadata/employees/{empId}/details
```

**Description:** Retrieves employee details including biometric registration status and approval eligibility mapping.

**Path Parameters:**
- `empId`: Required - Employee's ID (String)

**Response:**
```json
{
  "success": true,
  "data": {
    "empId": "123",
    "empName": "John Doe",
    "empCode": "EMP001",
    "empContact": "+1234567890",
    "empEmail": "<EMAIL>",
    "biometricRegistrationStatus": "APPROVED",
    "hasApprovalEligibilityMapping": true
  }
}
```

**Response Fields:**
- `empId`: String - Employee ID
- `empName`: String - Employee name
- `empCode`: String - Employee code
- `empContact`: String - Employee contact number
- `empEmail`: String - Employee email address
- `biometricRegistrationStatus`: String - Biometric registration status (APPROVED, PENDING, NOT_FOUND, etc.)
- `hasApprovalEligibilityMapping`: Boolean - Whether employee has any active approval eligibility mapping

**Biometric Status Values:**
- `APPROVED`: Employee has approved biometric registration
- `PENDING`: Employee has pending biometric registration
- `NOT_FOUND`: Employee has no biometric registration
- `REJECTED`: Employee's biometric registration was rejected

**Error Case:**
```json
{
  "success": false,
  "data": {
    "message": "Employee not found",
    "code": "EMPLOYEE_NOT_FOUND"
  }
}
```

## 4. Transfer Management APIs

### Create Transfer Request
```http
POST /api/v1/transfers
```

**Description:** Creates a new transfer request for an employee.

**Request Body:**
```json
{
  "empId": "string",           // Required: Employee ID
  "sourceUnitId": "string",    // Required: Source unit ID
  "destinationUnitId": "string", // Required: Destination unit ID
  "transferType": "PERMANENT", // Required: PERMANENT or TEMPORARY
  "startDate": "2024-01-01",   // Required: Start date (YYYY-MM-DD)
  "endDate": "2024-12-31",     // Optional: End date for temporary transfers
  "reason": "Promotion",       // Required: Transfer reason from available list
  "comment": "Additional notes about the transfer" // Optional: Additional comments
}
```

**Available Transfer Reasons:**
- "Promotion"
- "Organizational Restructuring"
- "Skill Development"
- "Project Requirement"
- "Workload Balancing"
- "Personal Request"
- "Performance Improvement"
- "Other"

**Response:**
```json
{
  "success": true,
  "data": {
    "transferRequestId": "123",
    "empId": "456",
    "sourceUnitId": "1",
    "destinationUnitId": "2",
    "transferType": "PERMANENT",
    "startDate": "2024-01-01",
    "endDate": null,
    "reason": "Promotion",
    "comment": "Additional notes about the transfer",
    "status": "PENDING",
    "approvalRequestId": "789",
    "createdDate": "2024-03-20T10:00:00Z",
    "createdBy": "user123"
  }
}
```

**Error Cases:**
1. Invalid Transfer Reason
```json
{
  "success": false,
  "data": {
    "message": "Invalid transfer reason. Please select from the available transfer reasons",
    "code": "INVALID_TRANSFER_REASON"
  }
}
```

2. Missing Required Fields
```json
{
  "success": false,
  "data": {
    "message": "Transfer reason is required",
    "code": "VALIDATION_ERROR"
  }
}
```

3. Same Unit Transfer
```json
{
  "success": false,
  "data": {
    "message": "Source and destination units cannot be the same",
    "code": "SAME_UNIT_TRANSFER"
  }
}
```

### Get Employee Transfer Metadata
```http
GET /api/v1/transfers/employee/{empId}/metadata
```

**Description:** Retrieves comprehensive transfer metadata for an employee including attendance eligible units, transfer reasons, and transfer types.

**Path Parameters:**
- `empId`: Required - Employee's ID (String)

**Response:**
```json
{
  "success": true,
  "data": {
    "empId": "123",
    "empName": "John Doe",
    "empCode": "EMP001",
    "attendanceEligibleUnits": [
      {
        "unitId": 1,
        "unitName": "Head Office",
        "unitCode": "HO001",
        "startDate": "2024-01-01",
        "endDate": "2024-12-31",
        "status": "ACTIVE"
      },
      {
        "unitId": 2,
        "unitName": "Branch Office",
        "unitCode": "BO001",
        "startDate": "2024-03-01",
        "endDate": null,
        "status": "ACTIVE"
      }
    ],
    "transferReasons": [
      {
        "name": "Promotion"
      },
      {
        "name": "Organizational Restructuring"
      },
      {
        "name": "Skill Development"
      },
      {
        "name": "Project Requirement"
      },
      {
        "name": "Workload Balancing"
      },
      {
        "name": "Personal Request"
      },
      {
        "name": "Performance Improvement"
      },
      {
        "name": "Other"
      }
    ],
    "transferTypes": ["PERMANENT", "TEMPORARY"]
  }
}
```

**Response Fields:**
- `empId`: String - Employee ID
- `empName`: String - Employee name
- `empCode`: String - Employee code
- `attendanceEligibleUnits`: Array - List of units where employee has attendance eligibility
  - `unitId`: Integer - Unique identifier for the unit
  - `unitName`: String - Name of the unit
  - `unitCode`: String - Code/identifier for the unit
  - `startDate`: Date (YYYY-MM-DD) - Start date of eligibility (null if no start date)
  - `endDate`: Date (YYYY-MM-DD) - End date of eligibility (null if no end date)
  - `status`: String - Status of the eligibility mapping
- `transferReasons`: Array - List of available transfer reasons
  - `name`: String - Display name for the transfer reason
- `transferTypes`: Array - List of available transfer types (PERMANENT, TEMPORARY)

**Error Case:**
```json
{
  "success": false,
  "data": {
    "message": "Employee not found",
    "code": "TRANSFER_NOT_FOUND"
  }
}
```

## Configuration
The attendance system has the following configurable parameters:
- `maxShiftHours`: 15 (Maximum hours allowed in a shift)
- `maxPunchesAllowedPerDay`: 5 (Maximum number of punches allowed per day)
- `duplicatePunchThresholdMinutes`: 30 (Minimum minutes required between punches)
- `dayStartHour`: 4 (4 AM - Start of attendance day)
- `dayEndHour`: 4 (3:59:59 AM - End of attendance day) 