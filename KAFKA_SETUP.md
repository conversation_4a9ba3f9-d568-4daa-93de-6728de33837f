# Kafka Setup Guide for Attendance Service

This guide will help you set up Kafka locally using Docker and connect it to your Spring Boot attendance service.

## 🚀 Quick Start

### 1. Start Kafka (Windows)
```bash
start-kafka.bat
```

### 1. Start Kafka (Linux/Mac)
```bash
chmod +x start-kafka.sh
./start-kafka.sh
```

### 2. Test Kafka Connection
```bash
# Windows
test-kafka-connection.bat

# Linux/Mac
chmod +x test-kafka-connection.sh
./test-kafka-connection.sh
```

### 3. Start Your Spring Boot Application
```bash
mvn spring-boot:run
```

## 📊 Kafka UI

Once Kafka is running, you can access the Kafka UI at:
- **URL**: http://localhost:8080
- **Features**: 
  - View topics and messages
  - Monitor consumer groups
  - Browse message content
  - Create/delete topics

## 🔧 Configuration

### Docker Compose Services

The `docker-compose.yml` includes:

1. **Zookeeper** (Port 2181)
   - Required for Kafka coordination
   - Manages Kafka cluster metadata

2. **Kafka** (Port 9092)
   - Message broker
   - Handles message storage and delivery
   - JMX monitoring on port 9101

3. **Kafka UI** (Port 8080)
   - Web interface for monitoring Kafka
   - Real-time message viewing

### Application Properties

Key Kafka configuration in `application.properties`:

```properties
# Kafka broker connection
spring.kafka.bootstrap-servers=localhost:9092

# Consumer configuration
spring.kafka.consumer.group-id=attendance-sync-group
spring.kafka.consumer.auto-offset-reset=earliest

# Topic name
kafka.topic.attendance-sync=attendance-sync-topic
```

## 🧪 Testing the Setup

### 1. Verify Kafka is Running
```bash
docker ps | grep kafka
```

### 2. Check Topics
```bash
docker exec kafka kafka-topics --list --bootstrap-server localhost:9092
```

### 3. Monitor Messages
```bash
# Real-time message monitoring
docker exec -it kafka kafka-console-consumer \
  --bootstrap-server localhost:9092 \
  --topic attendance-sync-topic \
  --from-beginning
```

### 4. Test Message Production
```bash
# Send test messages
docker exec -it kafka kafka-console-producer \
  --bootstrap-server localhost:9092 \
  --topic attendance-sync-topic
```

## 📝 Manual Commands

### Start Services
```bash
# Start all services
docker-compose up -d

# Start specific services
docker-compose up -d zookeeper kafka kafka-ui
```

### Stop Services
```bash
# Stop all services
docker-compose down

# Stop and remove volumes
docker-compose down -v
```

### View Logs
```bash
# View all logs
docker-compose logs

# View specific service logs
docker-compose logs -f kafka
docker-compose logs -f zookeeper
```

### Create Topic Manually
```bash
docker exec kafka kafka-topics \
  --create \
  --bootstrap-server localhost:9092 \
  --replication-factor 1 \
  --partitions 3 \
  --topic attendance-sync-topic
```

## 🔍 Troubleshooting

### Common Issues

1. **Kafka not starting**
   ```bash
   # Check if ports are in use
   netstat -an | grep 9092
   netstat -an | grep 2181
   
   # Restart services
   docker-compose down
   docker-compose up -d
   ```

2. **Connection refused**
   - Ensure Docker is running
   - Check if containers are healthy: `docker ps`
   - Verify ports are not blocked by firewall

3. **Topic not found**
   ```bash
   # Create topic manually
   docker exec kafka kafka-topics \
     --create \
     --bootstrap-server localhost:9092 \
     --replication-factor 1 \
     --partitions 3 \
     --topic attendance-sync-topic
   ```

4. **Consumer not receiving messages**
   - Check consumer group: `docker exec kafka kafka-consumer-groups --bootstrap-server localhost:9092 --list`
   - Reset offset if needed: `docker exec kafka kafka-consumer-groups --bootstrap-server localhost:9092 --group attendance-sync-group --reset-offsets --to-earliest --all-topics --execute`

### Health Checks

```bash
# Check if Kafka is responding
docker exec kafka kafka-topics --list --bootstrap-server localhost:9092

# Check consumer groups
docker exec kafka kafka-consumer-groups --bootstrap-server localhost:9092 --list

# Check topic details
docker exec kafka kafka-topics --describe --bootstrap-server localhost:9092 --topic attendance-sync-topic
```

## 📈 Monitoring

### Kafka UI Features
- **Topics**: View all topics and their configurations
- **Messages**: Browse and search messages
- **Consumers**: Monitor consumer groups and lag
- **Brokers**: View broker health and metrics

### Application Logs
Monitor your Spring Boot application logs for:
- Kafka connection status
- Message production/consumption
- Error handling and retries
- Sync processing status

## 🔄 Data Flow

1. **Attendance Punch** → Creates attendance record
2. **Sync Service** → Queues message to Kafka
3. **Kafka Consumer** → Processes sync message
4. **External API** → Sends data to payroll provider
5. **Sync Record** → Updates sync status

## 🛠️ Development Tips

1. **Use Kafka UI** for debugging message flow
2. **Monitor consumer lag** to ensure processing speed
3. **Check application logs** for detailed error information
4. **Use manual acknowledgment** for better error handling
5. **Test with small batches** before scaling up

## 📚 Additional Resources

- [Kafka Documentation](https://kafka.apache.org/documentation/)
- [Spring Kafka Documentation](https://docs.spring.io/spring-kafka/docs/current/reference/html/)
- [Docker Compose Documentation](https://docs.docker.com/compose/) 