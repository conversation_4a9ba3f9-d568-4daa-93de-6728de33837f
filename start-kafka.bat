@echo off
echo 🚀 Starting Kafka with Docker Compose...

REM Start Kafka and Zookeeper
docker-compose up -d zookeeper kafka kafka-ui

echo ⏳ Waiting for Kaf<PERSON> to be ready...
timeout /t 30 /nobreak > nul

echo ✅ Kafka is ready!
echo.
echo 📊 Kafka UI is available at: http://localhost:8080
echo 🔗 Kafka broker is available at: localhost:9092
echo 🔗 Zookeeper is available at: localhost:2181
echo.
echo 📋 Useful commands:
echo   - View logs: docker-compose logs -f kafka
echo   - Stop Kafka: docker-compose down
echo   - Restart Kafka: docker-compose restart kafka
echo.
echo 🧪 To test the setup, you can:
echo   1. Open Kafka UI at http://localhost:8080
echo   2. Check if 'attendance-sync-topic' is created automatically
echo   3. Start your Spring Boot application
echo   4. Make an attendance punch request to trigger sync
echo.
echo 🔍 To monitor messages:
echo   - Use Kafka UI to view topics and messages
echo   - Check application logs for sync processing
pause 