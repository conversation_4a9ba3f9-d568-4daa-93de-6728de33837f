<?xml version="1.0" encoding="UTF-8"?>
<MithraRuntime>

    <MithraRuntimeConfiguration>
        <ConnectionManager className="com.gs.fw.common.mithra.connectionmanager.XAConnectionManager">

            <Property name="resourceName" value="attendance_xa"/>
            <Property name="loginTimeoutSeconds" value="10"/>
            <Property name="poolName" value="attendance_pool"/>
            <Property name="initialPoolSize" value="1"/>
            <Property name="poolIncrementSize" value="1"/>
            <Property name="maxPoolSize" value="10"/>
            <Property name="minPoolSize" value="1"/>
            <Property name="acquireRetryAttempts" value="3"/>
            <Property name="acquireRetryDelay" value="1000"/>
            <Property name="maxIdleTime" value="60"/>
            <Property name="maxConnectionAge" value="120"/>
            <Property name="checkoutTimeout" value="3000"/>
            <Property name="numHelperThreads" value="3"/>
            <Property name="idleConnectionTestPeriod" value="30"/>
            <Property name="automaticTestTable" value="DUAL"/>
            <Property name="testConnectionOnCheckin" value="false"/>
            <Property name="testConnectionOnCheckout" value="false"/>
            <Property name="maxStatements" value="0"/>
            <Property name="maxStatementsPerConnection" value="0"/>
            <Property name="debugUnreturnedConnectionStackTraces" value="false"/>
            <Property name="unreturnedConnectionTimeout" value="0"/>

            <!-- Database connection properties -->
            <Property name="driverClassName" value="com.mysql.cj.jdbc.Driver"/>
            <Property name="url" value="${spring.datasource.url}"/>
            <Property name="user" value="${spring.datasource.username}"/>
            <Property name="password" value="${spring.datasource.password}"/>

            <!-- MySQL specific properties -->
            <Property name="useUnicode" value="true"/>
            <Property name="characterEncoding" value="UTF-8"/>
            <Property name="serverTimezone" value="Asia/Kolkata"/>
            <Property name="useSSL" value="false"/>
            <Property name="allowPublicKeyRetrieval" value="true"/>
            <Property name="rewriteBatchedStatements" value="true"/>
        </ConnectionManager>
    </MithraRuntimeConfiguration>

    <classpath>
        <include>src/main/resources/*.xml</include>
    </classpath>

    <MithraObjectConfiguration
            className="com.stpl.tech.attendance.domain.EmpShiftMapping"
            cacheType="partial"/>
</MithraRuntime>
