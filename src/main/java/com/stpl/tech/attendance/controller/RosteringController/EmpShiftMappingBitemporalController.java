package com.stpl.tech.attendance.controller.RosteringController;

import com.stpl.tech.attendance.dto.RosteringDTO.EmpShiftMappingDTO;
import com.stpl.tech.attendance.dto.RosteringDTO.EmpShiftUpdateRequestDTO;
import com.stpl.tech.attendance.service.RosteringService.EmpShiftMappingBitemporalService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;

/**
 * REST Controller for bitemporal employee shift mapping operations
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/rostering/bitemporal/emp-shift-mapping")
@RequiredArgsConstructor
@Tag(name = "Employee Shift Mapping Bitemporal", description = "Bitemporal operations for employee shift mappings")
public class EmpShiftMappingBitemporalController {

    private final EmpShiftMappingBitemporalService bitemporalService;

    @PostMapping
    @Operation(summary = "Create employee shift mapping with bitemporal support")
    public ResponseEntity<EmpShiftMappingDTO> createEmpShiftMapping(
            @Valid @RequestBody EmpShiftUpdateRequestDTO request,
            @Parameter(description = "Business date for the operation")
            @RequestParam(required = false) 
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime businessDate) {
        
        log.info("Creating bitemporal employee shift mapping: {}", request);
        
        Timestamp businessTimestamp = businessDate != null ? 
            Timestamp.valueOf(businessDate) : new Timestamp(System.currentTimeMillis());
        
        EmpShiftMappingDTO result = bitemporalService.createEmpShiftMapping(request, businessTimestamp);
        return ResponseEntity.ok(result);
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update employee shift mapping with bitemporal support")
    public ResponseEntity<EmpShiftMappingDTO> updateEmpShiftMapping(
            @PathVariable Integer id,
            @Valid @RequestBody EmpShiftUpdateRequestDTO request,
            @Parameter(description = "Business date for the operation")
            @RequestParam(required = false) 
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime businessDate) {
        
        log.info("Updating bitemporal employee shift mapping: {}", request);
        
        request.setId(id);
        Timestamp businessTimestamp = businessDate != null ? 
            Timestamp.valueOf(businessDate) : new Timestamp(System.currentTimeMillis());
        
        EmpShiftMappingDTO result = bitemporalService.updateEmpShiftMapping(request, businessTimestamp);
        return ResponseEntity.ok(result);
    }

    @DeleteMapping("/{id}/terminate")
    @Operation(summary = "Terminate employee shift mapping")
    public ResponseEntity<Void> terminateEmpShiftMapping(
            @PathVariable Integer id,
            @Parameter(description = "Business date for the operation")
            @RequestParam(required = false) 
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime businessDate,
            @Parameter(description = "End date for the mapping")
            @RequestParam(required = false) 
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {
        
        log.info("Terminating employee shift mapping: {}", id);
        
        Timestamp businessTimestamp = businessDate != null ? 
            Timestamp.valueOf(businessDate) : new Timestamp(System.currentTimeMillis());
        Timestamp endTimestamp = endDate != null ? 
            Timestamp.valueOf(endDate) : new Timestamp(System.currentTimeMillis());
        
        bitemporalService.terminateEmpShiftMapping(id, businessTimestamp, endTimestamp);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/employee/{empId}/current")
    @Operation(summary = "Get current shift for employee at specific business date")
    public ResponseEntity<EmpShiftMappingDTO> getCurrentShiftByEmpId(
            @PathVariable Integer empId,
            @Parameter(description = "Business date for the query")
            @RequestParam(required = false) 
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime businessDate) {
        
        log.debug("Getting current shift for employee: {} at business date: {}", empId, businessDate);
        
        Timestamp businessTimestamp = businessDate != null ? 
            Timestamp.valueOf(businessDate) : new Timestamp(System.currentTimeMillis());
        
        EmpShiftMappingDTO result = bitemporalService.findCurrentShiftByEmpId(empId, businessTimestamp);
        return result != null ? ResponseEntity.ok(result) : ResponseEntity.notFound().build();
    }

    @GetMapping("/employee/{empId}/date-range")
    @Operation(summary = "Get shift mappings for employee within date range")
    public ResponseEntity<List<EmpShiftMappingDTO>> getShiftMappingsByDateRange(
            @PathVariable Integer empId,
            @Parameter(description = "Start date")
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @Parameter(description = "End date")
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate,
            @Parameter(description = "Business date for the query")
            @RequestParam(required = false) 
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime businessDate) {
        
        log.debug("Getting shift mappings for employee: {} between {} and {}", empId, startDate, endDate);
        
        Timestamp businessTimestamp = businessDate != null ? 
            Timestamp.valueOf(businessDate) : new Timestamp(System.currentTimeMillis());
        
        List<EmpShiftMappingDTO> result = bitemporalService.findShiftMappingsByEmpIdAndDateRange(
            empId, Timestamp.valueOf(startDate), Timestamp.valueOf(endDate), businessTimestamp);
        
        return ResponseEntity.ok(result);
    }

    @GetMapping("/shift/{shiftId}/employees")
    @Operation(summary = "Get employees assigned to shift at specific business date")
    public ResponseEntity<List<EmpShiftMappingDTO>> getEmployeesByShiftId(
            @PathVariable Integer shiftId,
            @Parameter(description = "Business date for the query")
            @RequestParam(required = false) 
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime businessDate) {
        
        log.debug("Getting employees for shift: {} at business date: {}", shiftId, businessDate);
        
        Timestamp businessTimestamp = businessDate != null ? 
            Timestamp.valueOf(businessDate) : new Timestamp(System.currentTimeMillis());
        
        List<EmpShiftMappingDTO> result = bitemporalService.findEmployeesByShiftId(shiftId, businessTimestamp);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/employee/{empId}/history")
    @Operation(summary = "Get shift mapping history for employee")
    public ResponseEntity<List<EmpShiftMappingDTO>> getShiftMappingHistory(@PathVariable Integer empId) {
        log.debug("Getting shift mapping history for employee: {}", empId);
        
        List<EmpShiftMappingDTO> result = bitemporalService.findShiftMappingHistory(empId);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/{id}/as-of")
    @Operation(summary = "Get shift mapping as of specific business and processing dates")
    public ResponseEntity<EmpShiftMappingDTO> getShiftMappingAsOf(
            @PathVariable Integer id,
            @Parameter(description = "Business date")
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime businessDate,
            @Parameter(description = "Processing date")
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime processingDate) {
        
        log.debug("Getting shift mapping as of business date: {} and processing date: {}", 
                 businessDate, processingDate);
        
        EmpShiftMappingDTO result = bitemporalService.findShiftMappingAsOf(
            id, Timestamp.valueOf(businessDate), Timestamp.valueOf(processingDate));
        
        return result != null ? ResponseEntity.ok(result) : ResponseEntity.notFound().build();
    }

    @GetMapping("/{id}/versions")
    @Operation(summary = "Get all versions of shift mapping across time")
    public ResponseEntity<List<EmpShiftMappingDTO>> getAllVersionsOfShiftMapping(@PathVariable Integer id) {
        log.debug("Getting all versions of shift mapping: {}", id);
        
        List<EmpShiftMappingDTO> result = bitemporalService.findAllVersionsOfShiftMapping(id);
        return ResponseEntity.ok(result);
    }

    @PostMapping("/bulk-update")
    @Operation(summary = "Bulk update shift mappings")
    public ResponseEntity<List<EmpShiftMappingDTO>> bulkUpdateShiftMappings(
            @Valid @RequestBody List<EmpShiftUpdateRequestDTO> requests,
            @Parameter(description = "Business date for the operation")
            @RequestParam(required = false) 
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime businessDate) {
        
        log.info("Bulk updating {} shift mappings", requests.size());
        
        Timestamp businessTimestamp = businessDate != null ? 
            Timestamp.valueOf(businessDate) : new Timestamp(System.currentTimeMillis());
        
        List<EmpShiftMappingDTO> result = bitemporalService.bulkUpdateShiftMappings(requests, businessTimestamp);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/active")
    @Operation(summary = "Get active shift mappings as of specific business date")
    public ResponseEntity<List<EmpShiftMappingDTO>> getActiveShiftMappingsAsOf(
            @Parameter(description = "Business date for the query")
            @RequestParam(required = false) 
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime businessDate) {
        
        log.debug("Getting active shift mappings as of: {}", businessDate);
        
        Timestamp businessTimestamp = businessDate != null ? 
            Timestamp.valueOf(businessDate) : new Timestamp(System.currentTimeMillis());
        
        List<EmpShiftMappingDTO> result = bitemporalService.findActiveShiftMappingsAsOf(businessTimestamp);
        return ResponseEntity.ok(result);
    }

    @PostMapping("/correct-historical")
    @Operation(summary = "Correct historical shift mapping")
    public ResponseEntity<EmpShiftMappingDTO> correctHistoricalShiftMapping(
            @Valid @RequestBody EmpShiftUpdateRequestDTO request,
            @Parameter(description = "Business date for the correction")
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime businessDate,
            @Parameter(description = "Processing date for the correction")
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime processingDate) {
        
        log.info("Correcting historical shift mapping: {}", request);
        
        EmpShiftMappingDTO result = bitemporalService.correctHistoricalShiftMapping(
            request, Timestamp.valueOf(businessDate), Timestamp.valueOf(processingDate));
        
        return ResponseEntity.ok(result);
    }

    @GetMapping("/employee/{empId}/overlapping-check")
    @Operation(summary = "Check for overlapping shift mappings")
    public ResponseEntity<Boolean> checkOverlappingShiftMappings(
            @PathVariable Integer empId,
            @Parameter(description = "Start date")
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @Parameter(description = "End date")
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate,
            @Parameter(description = "Mapping ID to exclude from check")
            @RequestParam(required = false) Integer excludeMappingId,
            @Parameter(description = "Business date for the check")
            @RequestParam(required = false) 
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime businessDate) {
        
        log.debug("Checking for overlapping shift mappings for employee: {}", empId);
        
        Timestamp businessTimestamp = businessDate != null ? 
            Timestamp.valueOf(businessDate) : new Timestamp(System.currentTimeMillis());
        
        boolean hasOverlapping = bitemporalService.hasOverlappingShiftMappings(
            empId, 
            Timestamp.valueOf(startDate), 
            Timestamp.valueOf(endDate), 
            excludeMappingId != null ? excludeMappingId : -1,
            businessTimestamp
        );
        
        return ResponseEntity.ok(hasOverlapping);
    }
}
