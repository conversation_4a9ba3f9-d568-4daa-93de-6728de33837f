package com.stpl.tech.attendance.service.RosteringService;

import com.stpl.tech.attendance.dto.RosteringDTO.EmpShiftMappingDTO;
import com.stpl.tech.attendance.dto.RosteringDTO.EmpShiftUpdateRequestDTO;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Service interface for bitemporal operations on EmpShiftMapping using Reladomo
 */
public interface EmpShiftMappingBitemporalService {

    /**
     * Create a new employee shift mapping with bitemporal support
     */
    EmpShiftMappingDTO createEmpShiftMapping(EmpShiftUpdateRequestDTO request, Timestamp businessDate);

    /**
     * Update an existing employee shift mapping with bitemporal support
     */
    EmpShiftMappingDTO updateEmpShiftMapping(EmpShiftUpdateRequestDTO request, Timestamp businessDate);

    /**
     * Terminate an employee shift mapping (set end date)
     */
    void terminateEmpShiftMapping(Integer mappingId, Timestamp businessDate, Timestamp endDate);

    /**
     * Find current active shift mapping for an employee at a specific business date
     */
    EmpShiftMappingDTO findCurrentShiftByEmpId(Integer empId, Timestamp businessDate);

    /**
     * Find all shift mappings for an employee within a date range
     */
    List<EmpShiftMappingDTO> findShiftMappingsByEmpIdAndDateRange(
        Integer empId, 
        Timestamp startDate, 
        Timestamp endDate, 
        Timestamp businessDate
    );

    /**
     * Find all employees assigned to a shift at a specific business date
     */
    List<EmpShiftMappingDTO> findEmployeesByShiftId(Integer shiftId, Timestamp businessDate);

    /**
     * Find shift mapping history for an employee
     */
    List<EmpShiftMappingDTO> findShiftMappingHistory(Integer empId);

    /**
     * Check if employee has overlapping shift mappings
     */
    boolean hasOverlappingShiftMappings(
        Integer empId, 
        Timestamp startDate, 
        Timestamp endDate, 
        Integer excludeMappingId,
        Timestamp businessDate
    );

    /**
     * Get shift mapping as of a specific business and processing date
     */
    EmpShiftMappingDTO findShiftMappingAsOf(
        Integer mappingId, 
        Timestamp businessDate, 
        Timestamp processingDate
    );

    /**
     * Get all versions of a shift mapping across time
     */
    List<EmpShiftMappingDTO> findAllVersionsOfShiftMapping(Integer mappingId);

    /**
     * Bulk update shift mappings for multiple employees
     */
    List<EmpShiftMappingDTO> bulkUpdateShiftMappings(
        List<EmpShiftUpdateRequestDTO> requests, 
        Timestamp businessDate
    );

    /**
     * Find active shift mappings at a specific point in time
     */
    List<EmpShiftMappingDTO> findActiveShiftMappingsAsOf(Timestamp businessDate);

    /**
     * Correct a historical shift mapping (insert with past processing date)
     */
    EmpShiftMappingDTO correctHistoricalShiftMapping(
        EmpShiftUpdateRequestDTO request, 
        Timestamp businessDate, 
        Timestamp processingDate
    );
}
