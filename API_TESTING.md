# Attendance Service API Testing Documentation

## Overview
This document provides comprehensive testing guidelines and scenarios for the Attendance Service APIs. It includes test cases, request/response examples, and validation points for each endpoint.

## Base URL
```
http://localhost:8080
```

## Common Response Format
All APIs return responses in the following format:
```json
{
    "success": boolean,
    "data": object,
    "error": {
        "code": string,
        "message": string,
        "details": object
    },
    "pagination": {
        "pageNumber": number,
        "pageSize": number,
        "totalElements": number,
        "totalPages": number
    }
}
```

## 1. Attendance Management APIs

### 1.1 Mark Attendance (`POST /attendance/punch`)

#### Test Scenarios

1. **Valid Check-in**
   - **Request**:
   ```json
   {
       "employeeId": 123,
       "base64Image": "base64_encoded_image_string",
       "unitId": 456,
       "punchTime": "2024-03-20T09:00:00Z",
       "macAddress": "00:1A:2B:3C:4D:5E",
       "geoLocation": "12.9716,77.5946",
       "createdBy": "SYSTEM",
       "remarks": "Regular check-in",
       "isSpecialCase": false,
       "punchType": "CHECKIN"
   }
   ```
   - **Expected Response**: Success with punch details
   - **Validation Points**:
     - Verify punch time is recorded correctly
     - Check if image is stored and URL is returned
     - Validate employee details in response

2. **Invalid Employee ID**
   - **Request**: Same as above with non-existent employeeId
   - **Expected Response**: Error with appropriate message
   - **Validation Points**:
     - Error code should be 404
     - Error message should indicate invalid employee

3. **Missing Required Fields**
   - **Request**: Omit required fields like employeeId or base64Image
   - **Expected Response**: Validation error
   - **Validation Points**:
     - Error code should be 400
     - Error message should list missing fields

### 1.2 Get Current Day Attendance (`GET /attendance/today/{employeeId}`)

#### Test Scenarios

1. **Valid Employee with Attendance**
   - **Request**: GET with valid employeeId
   - **Expected Response**: Today's attendance records
   - **Validation Points**:
     - Check if all punch records are returned
     - Verify timestamps are in correct format
     - Validate attendance status

2. **Employee with No Attendance**
   - **Request**: GET with employeeId having no attendance
   - **Expected Response**: Empty or appropriate status
   - **Validation Points**:
     - Verify response indicates no attendance
     - Check if status is correctly set

### 1.3 Get Attendance History (`GET /attendance/{employeeId}`)

#### Test Scenarios

1. **Date Filter**
   - **Request**: GET with specific date
   - **Expected Response**: Attendance records for that date
   - **Validation Points**:
     - Verify only records for specified date are returned
     - Check date format handling

2. **Month/Year Filter**
   - **Request**: GET with month and year
   - **Expected Response**: Monthly attendance records
   - **Validation Points**:
     - Verify all records for the month are returned
     - Check if records are properly ordered

## 2. Biometric Registration APIs

### 2.1 Register Biometric (`POST /biometric/register`)

#### Test Scenarios

1. **Valid Registration**
   - **Request**:
   ```json
   {
       "empId": "EMP001",
       "unitId": "UNIT001",
       "deviceId": "DEV001",
       "latitude": 12.9716,
       "longitude": 77.5946,
       "base64Image": "base64_encoded_image"
   }
   ```
   - **Expected Response**: Success with registration details
   - **Validation Points**:
     - Verify biometric ID is generated
     - Check if image is stored
     - Validate location data

2. **Duplicate Registration**
   - **Request**: Same as above for already registered employee
   - **Expected Response**: Error indicating duplicate registration
   - **Validation Points**:
     - Error code should be appropriate
     - Error message should be clear

### 2.2 Deregister Biometric (`POST /biometric/deregister/{empId}`)

#### Test Scenarios

1. **Valid Deregistration**
   - **Request**: POST with valid empId
   - **Expected Response**: Success with deregistration details
   - **Validation Points**:
     - Verify status is updated
     - Check if all related data is cleared

2. **Non-existent Employee**
   - **Request**: POST with invalid empId
   - **Expected Response**: Error response
   - **Validation Points**:
     - Error code should be 404
     - Error message should be appropriate

## 3. Approval Management APIs

### 3.1 Get Pending Approvals (`GET /approvals/pending/{employeeId}`)

#### Test Scenarios

1. **Valid Approver**
   - **Request**: GET with valid approver ID
   - **Expected Response**: List of pending approvals
   - **Validation Points**:
     - Verify all pending requests are returned
     - Check if request details are complete

2. **No Pending Approvals**
   - **Request**: GET for approver with no pending requests
   - **Expected Response**: Empty list
   - **Validation Points**:
     - Verify empty list is returned
     - Check if pagination is correct

### 3.2 Process Approval Action (`POST /approvals/{requestId}/action`)

#### Test Scenarios

1. **Approve Request**
   - **Request**:
   ```json
   {
       "action": "APPROVE",
       "remarks": "Approved as per policy",
       "approverId": "APP001",
       "stepId": 1
   }
   ```
   - **Expected Response**: Updated approval status
   - **Validation Points**:
     - Verify status is updated
     - Check if next step is triggered
     - Validate remarks are saved

2. **Reject Request**
   - **Request**: Similar to above with "REJECT" action
   - **Expected Response**: Updated rejection status
   - **Validation Points**:
     - Verify rejection is recorded
     - Check if workflow is terminated
     - Validate rejection reason

## 4. Metadata Management APIs

### 4.1 Get Employee Metadata (`GET /metadata/employees`)

#### Test Scenarios

1. **Search with Term**
   - **Request**: GET with searchTerm parameter
   - **Expected Response**: Filtered employee list
   - **Validation Points**:
     - Verify search results are relevant
     - Check pagination is working
     - Validate search term handling

2. **Pagination**
   - **Request**: GET with page and size parameters
   - **Expected Response**: Paginated employee list
   - **Validation Points**:
     - Verify correct page is returned
     - Check total elements count
     - Validate page size limits

## General Testing Guidelines

1. **Authentication & Authorization**
   - Test all endpoints with valid and invalid tokens
   - Verify role-based access control
   - Test token expiration scenarios

2. **Input Validation**
   - Test boundary values for numeric fields
   - Verify date format handling
   - Test special characters in string fields

3. **Error Handling**
   - Test all possible error scenarios
   - Verify error messages are clear and helpful
   - Check error response format

4. **Performance Testing**
   - Test response times under load
   - Verify bulk operations performance
   - Check pagination with large datasets

5. **Integration Testing**
   - Test interaction with dependent services
   - Verify data consistency across services
   - Check error propagation

## Test Data Requirements

1. **Test Users**
   - Regular employees
   - Approvers
   - Administrators
   - Invalid users

2. **Test Devices**
   - Valid devices
   - Invalid devices
   - Unpaired devices

3. **Test Locations**
   - Valid coordinates
   - Invalid coordinates
   - Out of range coordinates

## Environment Setup

1. **Required Services**
   - Redis
   - Database
   - File storage

2. **Configuration**
   - API endpoints
   - Service URLs
   - Authentication tokens

3. **Test Data Setup**
   - Employee records
   - Device records
   - Approval workflows

## Reporting

1. **Test Results**
   - Pass/Fail status
   - Error messages
   - Response times

2. **Issues**
   - Bug reports
   - Enhancement requests
   - Performance issues

3. **Metrics**
   - API coverage
   - Test execution time
   - Success rate 