# Docker Setup for Attendance Service

This document describes the Docker setup and commands for running the Attendance Service.

## Prerequisites

- Docker installed on your machine
- Docker Compose installed on your machine
- Git repository cloned locally

## Service Components

The Docker Compose setup includes the following services:

1. **attendance-service**: The main application service
   - Port: 8080
   - Dependencies: Redis
   - Environment: Development profile

2. **redis**: Cache service
   - Port: 6379
   - Persistent storage through Docker volume

3. **Optional Services** (for local development):
   - master-service (Port: 8081)
   - biometric-service (Port: 8082)

## Basic Commands

### Starting Services

```bash
# Start all services in detached mode
docker-compose up -d

# Start specific services
docker-compose up -d attendance-service redis
```

### Viewing Logs

```bash
# View logs for all services
docker-compose logs -f

# View logs for specific service
docker-compose logs -f attendance-service

# View logs with timestamps
docker-compose logs -f --timestamps
```

### Stopping Services

```bash
# Stop all services
docker-compose down

# Stop specific services
docker-compose stop attendance-service redis
```

### Rebuilding Services

```bash
# Rebuild and restart all services
docker-compose up -d --build

# Rebuild and restart specific service
docker-compose up -d --build attendance-service
```

### Service Management

```bash
# Check service status
docker-compose ps

# Restart specific service
docker-compose restart attendance-service

# Access Redis CLI
docker-compose exec redis redis-cli
```

### Cleanup

```bash
# Remove all containers, networks, and volumes
docker-compose down -v

# Remove only containers and networks
docker-compose down
```

## Environment Configuration

The service uses the following environment variables:
- `SPRING_PROFILES_ACTIVE`: Set to 'dev' for development
- `SPRING_REDIS_HOST`: Redis host (default: redis)
- `SPRING_REDIS_PORT`: Redis port (default: 6379)
- `MASTER_SERVICE_URL`: URL for master service
- `BIOMETRIC_SERVICE_URL`: URL for biometric service

## Network Configuration

All services are connected through the `attendance-network` bridge network, allowing them to communicate with each other using service names as hostnames.

## Volume Management

Redis data is persisted using a Docker volume named `redis-data`. This ensures that cache data survives container restarts.

## Troubleshooting

1. **Service not starting:**
   ```bash
   # Check logs
   docker-compose logs attendance-service
   
   # Check service status
   docker-compose ps
   ```

2. **Redis connection issues:**
   ```bash
   # Check Redis logs
   docker-compose logs redis
   
   # Test Redis connection
   docker-compose exec redis redis-cli ping
   ```

3. **Network issues:**
   ```bash
   # Check network configuration
   docker network ls
   docker network inspect attendance-network
   ```

## Best Practices

1. Always use `docker-compose down` before stopping your development environment
2. Use `--build` flag when making changes to the Dockerfile
3. Check logs regularly for any issues
4. Keep the Redis data volume clean if not needed
5. Use specific service names when running commands to avoid affecting other services

## Additional Resources

- [Docker Documentation](https://docs.docker.com/)
- [Docker Compose Documentation](https://docs.docker.com/compose/)
- [Redis Documentation](https://redis.io/documentation) 