spring:
  application:
    name: flowable-ui
  mvc:
    cors:
      allowed-origins: "*"
      allowed-methods: "*"
      allowed-headers: "*"
  security:
    filter:
      order: -100
    basic:
      enabled: false

server:
  port: 8081
  servlet:
    context-path: /flowable-ui

# Temporarily disable security for testing
app:
  security:
    enabled: false

flowable:
  ui:
    app:
      # Engine URLs
      idm-url: ${FLOWABLE_ENGINE_URL:http://localhost:8080/api/v1}
      process-url: ${FLOWABLE_ENGINE_URL:http://localhost:8080/api/v1}
      task-url: ${FLOWABLE_ENGINE_URL:http://localhost:8080/api/v1}
      modeler-url: ${FLOWABLE_ENGINE_URL:http://localhost:8080/api/v1}
      rest-url: ${FLOWABLE_ENGINE_URL:http://localhost:8080/api/v1}
      
      # Admin credentials
      admin:
        user: ${FLOWABLE_ADMIN_USER:admin}
        password: ${FLOWABLE_ADMIN_PASSWORD:admin}
    
    # Enable UI components
    admin:
      enabled: true
      path: /admin
    task:
      enabled: true
      path: /task
    modeler:
      enabled: true
      path: /modeler
    rest:
      enabled: true
      path: /rest

# Logging configuration
logging:
  level:
    root: INFO
    com.stpl.tech.flowableui: DEBUG
    org.flowable: DEBUG
    org.springframework.web: DEBUG
    org.springframework.security: DEBUG 