package com.stpl.tech.flowableui.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;

@Configuration
@EnableWebSecurity
public class SecurityConfig {

    @Value("${app.security.enabled:true}")
    private boolean securityEnabled;

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        // Always disable CSRF and frame options for Flowable UI
        http
            .csrf(csrf -> csrf.disable())
            .headers(headers -> headers.frameOptions().disable())
            .sessionManagement(session -> session
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS));

        if (!securityEnabled) {
            // Completely disable security for testing
            http.authorizeHttpRequests(auth -> auth
                .anyRequest().permitAll()
            );
        } else {
            // Original security configuration
            http.authorizeHttpRequests(auth -> auth
                .requestMatchers("/flowable-ui/**").permitAll()
                .requestMatchers("/admin/**").permitAll()
                .requestMatchers("/task/**").permitAll()
                .requestMatchers("/modeler/**").permitAll()
                .requestMatchers("/rest/**").permitAll()
                .requestMatchers("/error/**").permitAll()
                .requestMatchers("/").permitAll()
                .anyRequest().authenticated()
            );
        }
        
        return http.build();
    }
} 